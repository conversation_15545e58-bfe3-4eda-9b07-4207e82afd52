import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Paper,
  TextField,
  IconButton,
  Typography,
  Avatar,
  Fab,
  Collapse,
  CircularProgress,
  Chip,
  Divider,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Send as SendIcon,
  Close as CloseIcon,
  SmartToy as BotIcon,
  Person as PersonIcon,
  Chat as ChatIcon,
  Minimize as MinimizeIcon
} from '@mui/icons-material';
import { useLanguage } from '../contexts/LanguageContext';
import geminiService from '../services/geminiService';

interface Message {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
}

const ChatBot: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [hasAutoOpened, setHasAutoOpened] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { language } = useLanguage();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Welcome message based on language
  const getWelcomeMessage = () => {
    switch (language) {
      case 'kn':
        return `ನಮಸ್ಕಾರ! 🌱 ನಾನು ದರ್ವಿ ಅಸಿಸ್ಟೆಂಟ್.

**ರೈತ ನೋಂದಣಿ ಕಾರ್ಯಕ್ರಮ - ₹4,725**
• ಉಚಿತ ಕೃಷಿ ಸಲಹೆ
• ಮಣ್ಣಿನ ಆರೋಗ್ಯ ಮೌಲ್ಯಮಾಪನ
• ತಜ್ಞರ ಬೆಂಬಲ

ನಿಮಗೆ ಏನು ತಿಳಿಯಬೇಕು?`;
      case 'hi':
        return `नमस्ते! 🌱 मैं दर्वी असिस्टेंट हूं।

**किसान पंजीकरण कार्यक्रम - ₹4,725**
• मुफ्त कृषि सलाह
• मिट्टी स्वास्थ्य मूल्यांकन
• विशेषज्ञ सहायता

आप क्या जानना चाहते हैं?`;
      default:
        return `Hello! 🌱 I'm Darvi Assistant.

**Farmer Registration Program - ₹4,725**
• Free agricultural consultation
• Soil health assessment
• Expert support & guidance

What would you like to know?`;
    }
  };

  // Quick action suggestions
  const getQuickActions = () => {
    switch (language) {
      case 'kn':
        return ['ರೈತ ನೋಂದಣಿ', 'ಸೇವೆಗಳು', 'ಬೆಲೆ ₹4,725', 'ಸಂಪರ್ಕ'];
      case 'hi':
        return ['किसान पंजीकरण', 'सेवाएं', 'मूल्य ₹4,725', 'संपर्क'];
      default:
        return ['Farmer Registration', 'Services', 'Price ₹4,725', 'Contact'];
    }
  };

  // Initialize with welcome message
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage: Message = {
        id: Date.now().toString(),
        text: getWelcomeMessage(),
        isBot: true,
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, language]);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    const scrollToBottom = () => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'end',
          inline: 'nearest'
        });
      }
    };

    // Small delay to ensure DOM is updated
    const timer = setTimeout(scrollToBottom, 100);
    return () => clearTimeout(timer);
  }, [messages]);

  // Auto-popup chatbot when user enters the website
  useEffect(() => {
    if (!hasAutoOpened) {
      const timer = setTimeout(() => {
        setIsOpen(true);
        setHasAutoOpened(true);
      }, 3000); // Show after 3 seconds

      return () => clearTimeout(timer);
    }
  }, [hasAutoOpened]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputValue.trim(),
      isBot: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Start response generation with timeout for better UX
      const responsePromise = geminiService.generateResponse(inputValue.trim());
      const timeoutPromise = new Promise<string>((_, reject) =>
        setTimeout(() => reject(new Error('Response timeout')), 10000)
      );

      const response = await Promise.race([responsePromise, timeoutPromise]);

      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: response,
        isBot: true,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botMessage]);
    } catch (error) {
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: language === 'kn' ? 'ಕ್ಷಮಿಸಿ, ತಾಂತ್ರಿಕ ಸಮಸ್ಯೆ ಇದೆ. ದಯವಿಟ್ಟು ಮತ್ತೆ ಪ್ರಯತ್ನಿಸಿ.' :
              language === 'hi' ? 'क्षमा करें, तकनीकी समस्या है। कृपया फिर से प्रयास करें।' :
              'Sorry, there was a technical issue. Please try again.',
        isBot: true,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickAction = (action: string) => {
    setInputValue(action);
    handleSendMessage();
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const formatMessage = (text: string) => {
    // Enhanced formatting for chatbot-style responses with mobile optimization
    const bulletMargin = isMobile ? '2px 0' : '6px 0';
    const bulletPadding = isMobile ? '4px' : '12px';
    const bulletSpacing = isMobile ? '4px' : '8px';

    return text
      .replace(/\*\*(.*?)\*\*/g, '<strong style="color: #1B4C35;">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // Format bullet points with mobile-responsive styling
      .replace(/^• (.+)$/gm, `<div style="margin: ${bulletMargin}; padding-left: ${bulletPadding}; display: flex; align-items: flex-start;"><span style="color: #1B4C35; margin-right: ${bulletSpacing}; font-weight: bold; font-size: ${isMobile ? '0.8em' : '1em'};">•</span><span style="font-size: ${isMobile ? '0.8em' : '1em'};">$1</span></div>`)
      .replace(/^- (.+)$/gm, `<div style="margin: ${bulletMargin}; padding-left: ${bulletPadding}; display: flex; align-items: flex-start;"><span style="color: #1B4C35; margin-right: ${bulletSpacing}; font-weight: bold; font-size: ${isMobile ? '0.8em' : '1em'};">•</span><span style="font-size: ${isMobile ? '0.8em' : '1em'};">$1</span></div>`)
      // Format numbered lists
      .replace(/^(\d+)\.\s(.+)$/gm, `<div style="margin: ${bulletMargin}; padding-left: ${bulletPadding}; display: flex; align-items: flex-start;"><span style="color: #1B4C35; margin-right: ${bulletSpacing}; font-weight: bold; font-size: ${isMobile ? '0.8em' : '1em'};">$1.</span><span style="font-size: ${isMobile ? '0.8em' : '1em'};">$2</span></div>`)
      // Highlight prices with mobile-friendly sizing
      .replace(/₹(\d+,?\d*)/g, `<strong style="color: #1B4C35; background: #e8f5e8; padding: ${isMobile ? '1px 4px' : '2px 6px'}; border-radius: 3px; font-size: ${isMobile ? '0.9em' : '1em'};">₹$1</strong>`)
      // Format phone numbers as clickable links with mobile optimization
      .replace(/(\+91\s?\d{5}\s?\d{5})/g, `<a href="tel:$1" style="color: #1B4C35; text-decoration: none; font-weight: bold; font-size: ${isMobile ? '0.85em' : '1em'};">📞 $1</a>`)
      // Format email addresses as clickable links with mobile optimization
      .replace(/([\w.-]+@[\w.-]+\.\w+)/g, `<a href="mailto:$1" style="color: #1B4C35; text-decoration: none; font-weight: bold; font-size: ${isMobile ? '0.85em' : '1em'};">✉️ $1</a>`)
      // Format website URLs as clickable links with mobile optimization
      .replace(/(https?:\/\/[^\s]+)/g, `<a href="$1" target="_blank" rel="noopener noreferrer" style="color: #1B4C35; text-decoration: none; font-weight: bold; font-size: ${isMobile ? '0.85em' : '1em'};">🌐 Visit Website</a>`)
      // Better paragraph spacing with mobile optimization
      .replace(/\n\n/g, `<div style="margin: ${isMobile ? '8px 0' : '12px 0'};"></div>`)
      .replace(/\n/g, '<br />');
  };

  return (
    <>
      {/* Chat Fab Button */}
      <Fab
        color="primary"
        size={isMobile ? 'medium' : 'large'}
        onClick={() => setIsOpen(!isOpen)}
        sx={{
          position: 'fixed',
          bottom: isMobile ? 12 : 24,
          right: isMobile ? 12 : 24,
          zIndex: 1000,
          width: isMobile ? 48 : 56,
          height: isMobile ? 48 : 56,
          background: 'linear-gradient(45deg, #1B4C35 30%, #2E7D32 90%)',
          '&:hover': {
            background: 'linear-gradient(45deg, #0A3622 30%, #1B4C35 90%)',
          },
          boxShadow: '0 4px 20px rgba(27, 76, 53, 0.3)',
          animation: !isOpen && !hasAutoOpened ? 'bounce 2s infinite' : 'none',
          '@keyframes bounce': {
            '0%, 20%, 50%, 80%, 100%': {
              transform: 'translateY(0)',
            },
            '40%': {
              transform: 'translateY(-10px)',
            },
            '60%': {
              transform: 'translateY(-5px)',
            },
          },
        }}
      >
        {isOpen ? <CloseIcon /> : <ChatIcon />}
      </Fab>

      {/* Chat Window */}
      <Collapse in={isOpen}>
        <Paper
          elevation={8}
          sx={{
            position: 'fixed',
            bottom: isMobile ? 70 : 100,
            right: isMobile ? 8 : 24,
            width: isMobile ? '280px' : 380,
            maxWidth: isMobile ? '280px' : 380,
            height: isMobile ? 'auto' : 480,
            maxHeight: isMobile ? '45vh' : 480,
            minHeight: isMobile ? '280px' : 400,
            zIndex: 999,
            borderRadius: isMobile ? '12px' : '16px',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
          }}
        >
          {/* Header */}
          <Box
            sx={{
              background: 'linear-gradient(45deg, #1B4C35 30%, #2E7D32 90%)',
              color: 'white',
              p: isMobile ? 1.5 : 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              minHeight: isMobile ? 48 : 56,
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: isMobile ? 0.5 : 1 }}>
              <Avatar sx={{
                bgcolor: 'rgba(255, 255, 255, 0.2)',
                width: isMobile ? 28 : 32,
                height: isMobile ? 28 : 32
              }}>
                <BotIcon fontSize={isMobile ? 'small' : 'medium'} />
              </Avatar>
              <Typography variant="h6" sx={{
                fontSize: isMobile ? '0.9rem' : '1rem',
                fontWeight: 600
              }}>
                Darvi Assistant
              </Typography>
            </Box>
            <Box>
              <IconButton
                size={isMobile ? 'small' : 'small'}
                onClick={() => setIsMinimized(!isMinimized)}
                sx={{
                  color: 'white',
                  mr: isMobile ? 0.25 : 0.5,
                  p: isMobile ? 0.5 : 1
                }}
              >
                <MinimizeIcon fontSize={isMobile ? 'small' : 'small'} />
              </IconButton>
              <IconButton
                size={isMobile ? 'small' : 'small'}
                onClick={() => setIsOpen(false)}
                sx={{
                  color: 'white',
                  p: isMobile ? 0.5 : 1
                }}
              >
                <CloseIcon fontSize={isMobile ? 'small' : 'small'} />
              </IconButton>
            </Box>
          </Box>

          <Collapse in={!isMinimized}>
            {/* Messages Area */}
            <Box
              sx={{
                flex: 1,
                overflow: 'auto',
                overflowX: 'hidden',
                p: isMobile ? 0.5 : 1,
                backgroundColor: '#f8f9fa',
                maxHeight: isMobile ? '200px' : '320px',
                minHeight: isMobile ? '150px' : '250px',
                '&::-webkit-scrollbar': {
                  width: isMobile ? '4px' : '6px',
                },
                '&::-webkit-scrollbar-track': {
                  background: '#f1f1f1',
                  borderRadius: '3px',
                },
                '&::-webkit-scrollbar-thumb': {
                  background: '#c1c1c1',
                  borderRadius: '3px',
                  '&:hover': {
                    background: '#a8a8a8',
                  },
                },
              }}
            >
              {messages.map((message) => (
                <Box
                  key={message.id}
                  sx={{
                    display: 'flex',
                    justifyContent: message.isBot ? 'flex-start' : 'flex-end',
                    mb: isMobile ? 1 : 2,
                    px: isMobile ? 0.25 : 1,
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      gap: isMobile ? 0.5 : 1,
                      maxWidth: isMobile ? '90%' : '80%',
                      flexDirection: message.isBot ? 'row' : 'row-reverse',
                    }}
                  >
                    <Avatar
                      sx={{
                        width: isMobile ? 24 : 28,
                        height: isMobile ? 24 : 28,
                        bgcolor: message.isBot ? '#1B4C35' : '#2196f3',
                        fontSize: '0.75rem',
                      }}
                    >
                      {message.isBot ? <BotIcon fontSize="small" /> : <PersonIcon fontSize="small" />}
                    </Avatar>
                    <Paper
                      elevation={2}
                      sx={{
                        p: isMobile ? 1 : 2,
                        borderRadius: message.isBot ?
                          (isMobile ? '10px 10px 10px 2px' : '16px 16px 16px 4px') :
                          (isMobile ? '10px 10px 2px 10px' : '16px 16px 4px 16px'),
                        backgroundColor: message.isBot ? 'white' : '#1B4C35',
                        color: message.isBot ? 'text.primary' : 'white',
                        fontSize: isMobile ? '0.75rem' : '0.875rem',
                        lineHeight: 1.3,
                        border: message.isBot ? '1px solid #e0e0e0' : 'none',
                        boxShadow: message.isBot ? '0 2px 8px rgba(0,0,0,0.1)' : '0 2px 8px rgba(27,76,53,0.3)',
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          fontSize: isMobile ? '0.75rem' : '0.875rem',
                          lineHeight: 1.3,
                        }}
                        dangerouslySetInnerHTML={{
                          __html: formatMessage(message.text)
                        }}
                      />
                    </Paper>
                  </Box>
                </Box>
              ))}

              {/* Loading indicator */}
              {isLoading && (
                <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 1, px: isMobile ? 0.5 : 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: isMobile ? 0.5 : 1 }}>
                    <Avatar sx={{
                      width: isMobile ? 24 : 28,
                      height: isMobile ? 24 : 28,
                      bgcolor: '#1B4C35'
                    }}>
                      <BotIcon fontSize="small" />
                    </Avatar>
                    <Paper elevation={1} sx={{
                      p: isMobile ? 1 : 1.5,
                      borderRadius: isMobile ? '10px' : '12px'
                    }}>
                      <CircularProgress size={isMobile ? 14 : 16} />
                    </Paper>
                  </Box>
                </Box>
              )}

              {/* Quick Actions */}
              {messages.length === 1 && !isLoading && (
                <Box sx={{ mt: isMobile ? 1 : 2, px: isMobile ? 0.5 : 1, mb: isMobile ? 0.5 : 1 }}>
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{
                      mb: isMobile ? 0.25 : 1,
                      display: 'block',
                      fontSize: isMobile ? '0.65rem' : '0.75rem'
                    }}
                  >
                    {language === 'kn' ? 'ತ್ವರಿತ ಪ್ರಶ್ನೆಗಳು:' :
                     language === 'hi' ? 'त्वरित प्रश्न:' : 'Quick questions:'}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: isMobile ? 0.25 : 0.5 }}>
                    {getQuickActions().slice(0, isMobile ? 3 : 4).map((action, index) => (
                      <Chip
                        key={index}
                        label={action}
                        size="small"
                        onClick={() => handleQuickAction(action)}
                        sx={{
                          cursor: 'pointer',
                          fontSize: isMobile ? '0.65rem' : '0.75rem',
                          height: isMobile ? 22 : 28,
                          '& .MuiChip-label': {
                            px: isMobile ? 0.75 : 1.5,
                            py: isMobile ? 0.25 : 0.5,
                          },
                          '&:hover': {
                            backgroundColor: 'primary.light',
                            color: 'white',
                          },
                        }}
                      />
                    ))}
                  </Box>
                </Box>
              )}

              <div ref={messagesEndRef} />
            </Box>

            <Divider />

            {/* Input Area */}
            <Box sx={{
              p: isMobile ? 0.75 : 1.5,
              backgroundColor: 'white',
              borderTop: '1px solid #e0e0e0'
            }}>
              <Box sx={{ display: 'flex', gap: isMobile ? 0.5 : 1, alignItems: 'flex-end' }}>
                <TextField
                  fullWidth
                  multiline
                  maxRows={isMobile ? 2 : 3}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder={
                    language === 'kn' ? 'ನಿಮ್ಮ ಪ್ರಶ್ನೆಯನ್ನು ಟೈಪ್ ಮಾಡಿ...' :
                    language === 'hi' ? 'अपना प्रश्न टाइप करें...' :
                    'Type your question...'
                  }
                  variant="outlined"
                  size={isMobile ? 'small' : 'small'}
                  disabled={isLoading}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: isMobile ? '16px' : '20px',
                      fontSize: isMobile ? '0.875rem' : '1rem',
                    },
                    '& .MuiInputBase-input': {
                      padding: isMobile ? '8px 12px' : '10px 14px',
                    },
                  }}
                />
                <IconButton
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isLoading}
                  size={isMobile ? 'small' : 'medium'}
                  sx={{
                    bgcolor: 'primary.main',
                    color: 'white',
                    width: isMobile ? 36 : 40,
                    height: isMobile ? 36 : 40,
                    '&:hover': {
                      bgcolor: 'primary.dark',
                    },
                    '&:disabled': {
                      bgcolor: 'grey.300',
                    },
                  }}
                >
                  <SendIcon fontSize={isMobile ? 'small' : 'small'} />
                </IconButton>
              </Box>
            </Box>
          </Collapse>
        </Paper>
      </Collapse>
    </>
  );
};

export default ChatBot;
