interface Config {
  googleScriptUrl: string;
  contactNumber: string;
  email: string;
  geminiApiKey: string;
  weatherApiKey: string;
}

const config: Config = {
  googleScriptUrl: process.env.REACT_APP_GOOGLE_SCRIPT_URL || '',
  contactNumber: process.env.REACT_APP_CONTACT_NUMBER || '',
  email: process.env.REACT_APP_EMAIL || '',
  geminiApiKey: process.env.REACT_APP_GEMINI_API_KEY || '',
  weatherApiKey: process.env.REACT_APP_WEATHER_API_KEY || '',
};

export const isConfigValid = () => {
  return !!config.googleScriptUrl;
};

export const isGeminiConfigValid = () => {
  return !!config.geminiApiKey;
};

export const isWeatherConfigValid = () => {
  return !!config.weatherApiKey;
};

export default config;