import React, { useEffect, useState } from 'react';
import {
  Typo<PERSON>,
  Button,
  Box,
  Grid,
  Card,
  Container,
  useTheme,
  keyframes
} from '@mui/material';
import { Link } from 'react-router-dom';
import EmojiNatureIcon from '@mui/icons-material/EmojiNature';
import GroupsIcon from '@mui/icons-material/Groups';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import RouterIcon from '@mui/icons-material/Router';
import BarChartIcon from '@mui/icons-material/BarChart';
import NatureIcon from '@mui/icons-material/Nature';
import PublicIcon from '@mui/icons-material/Public';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from '@mui/icons-material/Email';
import TerrainIcon from '@mui/icons-material/Terrain';
import ScienceIcon from '@mui/icons-material/Science';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import Footer from '../components/Footer';
import AnimatedText from '../components/AnimatedText';
import ImageSlider from '../components/ImageSlider';
import SectionContainer from '../components/SectionContainerFixed';
import TeamSection from '../components/TeamSection';
import TestimonialSection from '../components/TestimonialSection';
import FarmlandContactForm from '../components/FarmlandContactForm';
import ResearchCenterPopup from '../components/ResearchCenterPopup';
import { useLanguage } from '../contexts/LanguageContext';
import translations from '../translations';

// Import content from JSON files
import heroContent from '../content/home/<USER>';
import valuesContent from '../content/home/<USER>';
import iotFeaturesContent from '../content/home/<USER>';
import contactContent from '../content/home/<USER>';
import galleryContent from '../content/home/<USER>';

const HomePage = () => {
  const theme = useTheme();
  const { language } = useLanguage();
  const t = translations[language as keyof typeof translations];
  const [showResearchPopup, setShowResearchPopup] = useState(false);

  // Add animation effect when component mounts
  useEffect(() => {
    // Set document title when component mounts
    document.title = 'Darvi Group - Home';
    
    // Show research center popup after 2 seconds
    const timer = setTimeout(() => {
      setShowResearchPopup(true);
    }, 2000);
    
    return () => clearTimeout(timer);
  }, []);



  // Process values from JSON content
  const values = valuesContent.values.map(value => {
    // Map icon string to actual icon component
    let iconComponent;
    switch (value.icon) {
      case 'EmojiNature':
        iconComponent = <EmojiNatureIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />;
        break;
      case 'Groups':
        iconComponent = <GroupsIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />;
        break;
      case 'Lightbulb':
        iconComponent = <LightbulbIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />;
        break;
      case 'TrendingUp':
        iconComponent = <TrendingUpIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />;
        break;
      default:
        iconComponent = <EmojiNatureIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />;
    }

    return {
      icon: iconComponent,
      title: value.title[language as keyof typeof value.title] || value.title.en,
      description: value.description[language as keyof typeof value.description] || value.description.en
    };
  });

  // Process IoT features from JSON content
  const iotFeatures = iotFeaturesContent.features.map(feature => {
    // Map icon string to actual icon component
    let iconComponent;
    switch (feature.icon) {
      case 'Router':
        iconComponent = <RouterIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />;
        break;
      case 'BarChart':
        iconComponent = <BarChartIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />;
        break;
      case 'Nature':
        iconComponent = <NatureIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />;
        break;
      case 'Public':
        iconComponent = <PublicIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />;
        break;
      default:
        iconComponent = <RouterIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />;
    }

    return {
      icon: iconComponent,
      title: feature.title[language as keyof typeof feature.title] || feature.title.en,
      description: feature.description[language as keyof typeof feature.description] || feature.description.en
    };
  });



  // Process contact info from JSON content
  const contactInfo = contactContent.contactInfo.map(info => {
    // Map icon string to actual icon component
    let iconComponent;
    switch (info.icon) {
      case 'LocationOn':
        iconComponent = <LocationOnIcon sx={{ fontSize: 30, color: theme.palette.primary.main }} />;
        break;
      case 'Phone':
        iconComponent = <PhoneIcon sx={{ fontSize: 30, color: theme.palette.primary.main }} />;
        break;
      case 'Email':
        iconComponent = <EmailIcon sx={{ fontSize: 30, color: theme.palette.primary.main }} />;
        break;
      default:
        iconComponent = <LocationOnIcon sx={{ fontSize: 30, color: theme.palette.primary.main }} />;
    }

    return {
      icon: iconComponent,
      title: info.title[language as keyof typeof info.title] || info.title.en,
      content: info.content
    };
  });

  // Process hero slides from JSON content
  const heroSlides = heroContent.slides.map(slide => ({
    image: slide.image,
    title: slide.title[language as keyof typeof slide.title] || slide.title.en,
    description: slide.description[language as keyof typeof slide.description] || slide.description.en,
    url: slide.url
  }));

  // Process gallery images from JSON content
  const galleryImages = galleryContent.images;



  // Define blinking animation
  const blinkAnimation = keyframes`
    0%, 80% { background-color: rgba(76, 175, 80, 0.1); }
    40% { background-color: rgba(76, 175, 80, 0.3); }
  `;

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {/* Consultation Banner with Two Columns */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          py: { xs: 0.75, sm: 1 },
          px: { xs: 1, sm: 3 },
          mx: { xs: 0, sm: 0 },
          mt: 0,
          mb: 0,
          backgroundColor: 'rgba(76, 175, 80, 0.1)',
          animation: `${blinkAnimation} 3s infinite ease-in-out`,
          textAlign: 'center',
          width: '100%',
          overflow: 'hidden'
        }}
      >
        <Grid container spacing={2} sx={{ width: '100%', alignItems: 'center' }}>
          <Grid item xs={12} sm={6}>
            <Typography
              component={Link}
              to="/form"
              sx={{
                color: '#1B4C35',
                fontWeight: 600,
                fontSize: { xs: '0.75rem', sm: '0.85rem', md: '0.9rem' },
                textDecoration: 'none',
                width: '100%',
                py: 0.5,
                display: 'block',
                transition: 'transform 0.3s ease',
                '&:hover': {
                  textDecoration: 'underline',
                  transform: 'translateY(-2px)'
                }
              }}
            >
              {language === 'en' ? 'Get your on-site consultation now- Click here' :
              language === 'kn' ? 'ನಿಮ್ಮ ಆನ್-ಸೈಟ್ ಸಮಾಲೋಚನೆಯನ್ನು ಈಗ ಪಡೆಯಿರಿ- ಇಲ್ಲಿ ಕ್ಲಿಕ್ ಮಾಡಿ' :
              'अपना ऑन-साइट परामर्श अभी प्राप्त करें- यहां क्लिक करें'}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography
              component={Link}
              to="/research-center"
              sx={{
                color: '#1B4C35',
                fontWeight: 600,
                fontSize: { xs: '0.75rem', sm: '0.85rem', md: '0.9rem' },
                textDecoration: 'none',
                width: '100%',
                py: 0.5,
                display: 'block',
                transition: 'transform 0.3s ease',
                '&:hover': {
                  textDecoration: 'underline',
                  transform: 'translateY(-2px)'
                }
              }}
            >
              {language === 'en' ? 'Exclusive in Research Centre - Explore Now' :
              language === 'kn' ? 'ಸಂಶೋಧನಾ ಕೇಂದ್ರದಲ್ಲಿ ವಿಶೇಷ - ಈಗ ಅನ್ವೇಷಿಸಿ' :
              'अनुसंधान केंद्र में विशेष - अभी एक्सप्लोर करें'}
            </Typography>
          </Grid>
        </Grid>
      </Box>

      {/* Hero Section with Split Design */}
      <Box
        sx={{
          position: 'relative',
          color: 'black',
          mb: { xs: 2, sm: 3, md: 4 },
          mx: { xs: 1, sm: 1.5, md: 2 },
          overflow: 'hidden',
          height: { xs: 'auto', sm: '65vh', md: '80vh' },
          maxHeight: { xs: 'none', md: '800px' },
          minHeight: { xs: '500px', sm: '400px', md: '500px' },
          borderRadius: { xs: '12px', sm: '16px', md: '20px' },
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' }
        }}
      >
        {/* Left Content Section */}
        <Box
          sx={{
            width: { xs: '100%', md: '50%' },
            height: { xs: 'auto', md: '100%' },
            bgcolor: '#f8f9f6',
            position: 'relative',
            zIndex: 1,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            p: { xs: 2.5, sm: 4, md: 5 },
            py: { xs: 3, sm: 4, md: 5 },
            minHeight: { xs: '300px', sm: 'auto' }
          }}
        >
          <AnimatedText
            text={language === 'en' ? "Exploring Journey of" : language === 'kn' ? "ಸಾವಯವ ಕೃಷಿಯ" : "जैविक खेती का"}
            variant="h2"
            animation="fadeIn"
            delay={0.3}
            duration={0.8}
            sx={{
              fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2.2rem', lg: '2.8rem' },
              fontWeight: 700,
              color: '#4caf50',
              mb: { xs: 0.5, sm: 0.75, md: 1 },
              lineHeight: { xs: 1, sm: 1.05, md: 1.1 },
              fontFamily: "'Poppins', sans-serif"
            }}
          />
          <AnimatedText
            text={language === 'en' ? "Organic Farming" : language === 'kn' ? "ಅನ್ವೇಷಣಾತ್ಮಕ ಪಯಣ" : "अन्वेषण यात्रा"}
            variant="h2"
            animation="slideUp"
            delay={0.6}
            duration={0.8}
            sx={{
              fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2.2rem', lg: '2.8rem' },
              fontWeight: 700,
              color: '#1B4C35',
              mb: { xs: 1.5, sm: 2, md: 3 },
              lineHeight: { xs: 1, sm: 1.05, md: 1.1 },
              fontFamily: "'Poppins', sans-serif"
            }}
          />
          <AnimatedText
            text={language === 'en'
              ? "Dive into the world of sustainable agriculture with SustainaGrow, where we nurtur organic crops with care and commitment. Explore our diverse range of naturally-grown produce and join us in redefining farming for generations to come."
              : language === 'kn'
              ? "ಸಸ್ಟೈನಾಗ್ರೋದೊಂದಿಗೆ ಸುಸ್ಥಿರ ಕೃಷಿಯ ಜಗತ್ತಿಗೆ ಧುಮುಕಿ, ಅಲ್ಲಿ ನಾವು ಕಾಳಜಿ ಮತ್ತು ಬದ್ಧತೆಯೊಂದಿಗೆ ಸಾವಯವ ಬೆಳೆಗಳನ್ನು ಪೋಷಿಸುತ್ತೇವೆ. ನಮ್ಮ ವೈವಿಧ್ಯಮಯ ನೈಸರ್ಗಿಕವಾಗಿ ಬೆಳೆದ ಉತ್ಪನ್ನಗಳನ್ನು ಅನ್ವೇಷಿಸಿ ಮತ್ತು ಮುಂದಿನ ಪೀಳಿಗೆಗಳಿಗಾಗಿ ಕೃಷಿಯನ್ನು ಮರುವ್ಯಾಖ್ಯಾನಿಸುವಲ್ಲಿ ನಮ್ಮೊಂದಿಗೆ ಸೇರಿ."
              : "सस्टेनाग्रो के साथ टिकाऊ कृषि की दुनिया में उतरें, जहां हम देखभाल और प्रतिबद्धता के साथ जैविक फसलों का पोषण करते हैं। प्राकृतिक रूप से उगाए गए उत्पादों की हमारी विविध श्रृंखला का अन्वेषण करें और आने वाली पीढ़ियों के लिए खेती को फिर से परिभाषित करने में हमारे साथ जुड़ें।"
            }
            variant="body1"
            animation="fadeIn"
            delay={0.9}
            duration={0.8}
            sx={{
              fontSize: { xs: '0.85rem', sm: '0.9rem', md: '1rem' },
              mb: { xs: 2, sm: 3, md: 4 },
              color: '#555',
              maxWidth: '600px',
              lineHeight: { xs: 1.5, sm: 1.6, md: 1.7 },
              display: { xs: '-webkit-box', sm: 'block' },
              WebkitLineClamp: { xs: 3, sm: 'none' },
              WebkitBoxOrient: 'vertical',
              overflow: { xs: 'hidden', sm: 'visible' }
            }}
          />
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              gap: { xs: 1, sm: 2, md: 3 },
              mt: { xs: 1, sm: 2 },
              mb: { xs: 2, sm: 0 }, // Add bottom margin on mobile
              alignItems: { xs: 'center', sm: 'flex-start' },
              justifyContent: { xs: 'center', sm: 'flex-start' },
              width: { xs: '100%', sm: 'auto' }
            }}
          >

            <Button
              variant="outlined"
              size="medium"
              component="a"
              href="https://www.youtube.com/@DarviGroup/playlists"
              target="_blank"
              rel="noopener noreferrer"
              startIcon={
                <Box
                  sx={{
                    width: { xs: 18, sm: 20, md: 24 },
                    height: { xs: 18, sm: 20, md: 24 },
                    borderRadius: '50%',
                    bgcolor: 'rgba(76, 175, 80, 0.1)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <Box
                    component="span"
                    sx={{
                      width: 0,
                      height: 0,
                      borderTop: { xs: '4px solid transparent', sm: '5px solid transparent', md: '6px solid transparent' },
                      borderBottom: { xs: '4px solid transparent', sm: '5px solid transparent', md: '6px solid transparent' },
                      borderLeft: { xs: '6px solid #4caf50', sm: '8px solid #4caf50', md: '10px solid #4caf50' },
                      ml: 0.5
                    }}
                  />
                </Box>
              }
              sx={{
                color: '#4caf50',
                borderColor: '#4caf50',
                px: { xs: 2, sm: 2.5, md: 3 },
                py: { xs: 0.75, sm: 1, md: 1.25 },
                fontSize: { xs: '0.75rem', sm: '0.8rem', md: '0.9rem' },
                borderRadius: '4px',
                textTransform: 'none',
                fontWeight: 600,
                width: { xs: '80%', sm: 'auto' },
                mx: { xs: 'auto', sm: 0 }, // Center on mobile
                mb: { xs: 2, sm: 0 }, // Add bottom margin on mobile
                '&:hover': {
                  borderColor: '#43a047',
                  bgcolor: 'rgba(76, 175, 80, 0.05)',
                  transform: 'translateY(-3px)'
                },
                transition: 'all 0.3s ease'
              }}
            >
              {language === 'en' ? 'Watch Videos' : language === 'kn' ? 'ವೀಡಿಯೊಗಳನ್ನು ವೀಕ್ಷಿಸಿ' : 'वीडियो देखें'}
            </Button>
          </Box>

          {/* Service Icons */}
          <Box
            sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: { xs: 1, sm: 1.5, md: 2 },
              mt: { xs: 1.5, sm: 3, md: 4 },
              justifyContent: { xs: 'center', sm: 'flex-start' },
              mb: { xs: 1, sm: 0 },
              width: '100%'
            }}
          >
            {[
              { icon: '🌱', title: language === 'en' ? 'Organic Farming' : language === 'kn' ? 'ಸಾವಯವ ಕೃಷಿ' : 'जैविक खेती' },
              { icon: '🌍', title: language === 'en' ? 'Soil Health' : language === 'kn' ? 'ಮಣ್ಣಿನ ಆರೋಗ್ಯ' : 'मिट्टी का स्वास्थ्य' },
              { icon: '🌿', title: language === 'en' ? 'Crop Diversity' : language === 'kn' ? 'ಬೆಳೆ ವೈವಿಧ್ಯತೆ' : 'फसल विविधता' }
            ].map((service, index) => (
              <Box
                key={index}
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  p: { xs: 0.5, sm: 0.75, md: 1.5 },
                  bgcolor: 'white',
                  borderRadius: '8px',
                  width: { xs: 'calc(30% - 8px)', sm: 'calc(30% - 12px)', md: 'calc(30% - 16px)' },
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-3px)',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                  }
                }}
              >
                <Typography
                  sx={{
                    fontSize: { xs: '1rem', sm: '1.3rem', md: '1.8rem' },
                    mb: { xs: 0.25, sm: 0.5, md: 0.75 }
                  }}
                >
                  {service.icon}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' },
                    fontWeight: 500,
                    color: '#555',
                    textAlign: 'center',
                    wordBreak: 'break-word'
                  }}
                >
                  {service.title}
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>

        {/* Curved Divider between sections */}
        <Box
          sx={{
            position: 'absolute',
            top: { xs: '50%', md: 0 },
            bottom: { xs: 'auto', md: 0 },
            left: { xs: 0, md: '50%' },
            width: { xs: '100%', md: '80px' },
            height: { xs: '80px', md: '100%' },
            transform: { xs: 'translateY(-50%)', md: 'translateX(-50%)' },
            zIndex: 5,
            display: { xs: 'none', md: 'flex' }, // Hide on mobile to prevent alignment issues
            justifyContent: 'center',
            alignItems: 'center',
            pointerEvents: 'none'
          }}
        >
          <Box
            sx={{
              width: { xs: '100%', md: '100px' },
              height: { xs: '100px', md: '100%' },
              position: 'relative',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: { xs: '50%', md: 0 },
                left: { xs: 0, md: '50%' },
                width: { xs: '100%', md: '100px' },
                height: { xs: '100px', md: '100%' },
                background: '#f8f9f6',
                borderRadius: { xs: '0 0 50% 50%', md: '0 50% 50% 0' },
                transform: { xs: 'translateY(-50%)', md: 'translateX(-50%)' },
                zIndex: 2
              }
            }}
          />
        </Box>

        {/* Right Image Section with Slider */}
        <Box
          sx={{
            width: { xs: '100%', md: '50%' },
            height: { xs: '220px', sm: '300px', md: '100%' },
            position: 'relative',
            bgcolor: '#e8f5e9',
            overflow: 'hidden'
          }}
        >
          {/* Image Slider */}
          <ImageSlider
            slides={heroSlides}
            height="100%"
            showArrows={false}
            showDots={true}
            showCaption={false}
            borderRadius={0}
            effect="fade"
            autoPlayInterval={2000}
          />
        </Box>
      </Box>

      {/* Services Section */}
      <SectionContainer
        title={<AnimatedText text={language === 'en' ? "Our Services" : language === 'kn' ? "ನಮ್ಮ ಸೇವೆಗಳು" : "हमारी सेवाएं"} animation="fadeIn" delay={0.2} duration={0.8} variant="h3" />}
        subtitle={language === 'en' ? "Comprehensive agricultural solutions for your farming needs" : language === 'kn' ? "ನಿಮ್ಮ ಕೃಷಿ ಅಗತ್ಯಗಳಿಗೆ ಸಮಗ್ರ ಕೃಷಿ ಪರಿಹಾರಗಳು" : "आपकी कृषि आवश्यकताओं के लिए व्यापक कृषि समाधान"}
        bgColor="#f5f5f5"
        textColor="#333"
        linkTo="/services"
        linkText={language === 'en' ? "View All Services" : language === 'kn' ? "ಎಲ್ಲಾ ಸೇವೆಗಳನ್ನು ವೀಕ್ಷಿಸಿ" : "सभी सेवाएं देखें"}
        paddingTop={5}
        paddingBottom={5}
      >
        <Grid container spacing={4}>
          {/* Service 1: Consultations - New Design with Image */}
          <Grid item xs={12} md={6}>
            <Box
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                borderRadius: 2,
                bgcolor: 'white',
                boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                overflow: 'hidden',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 10px 20px rgba(0,0,0,0.1)'
                }
              }}
            >
              <Box
                sx={{
                  height: '200px',
                  width: '100%',
                  position: 'relative',
                  overflow: 'hidden',
                  bgcolor: 'rgba(46, 125, 50, 0.1)'
                }}
              >
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    bgcolor: 'rgba(46, 125, 50, 0.1)'
                  }}
                >
                  <LightbulbIcon sx={{ fontSize: 80, color: theme.palette.primary.main, opacity: 0.7 }} />
                </Box>
              </Box>
              
              <Box sx={{ p: 3 }}>
                <Typography variant="h5" component="h3" gutterBottom fontWeight={600} sx={{ textAlign: 'left' }}>
                  {language === 'en' ? 'Consultations' : language === 'kn' ? 'ಸಮಾಲೋಚನೆಗಳು' : 'परामर्श'}
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'left', mb: 2 }}>
                  {language === 'en' ? 'Expert agricultural advice tailored to your specific needs' : 
                   language === 'kn' ? 'ನಿಮ್ಮ ನಿರ್ದಿಷ್ಟ ಅಗತ್ಯಗಳಿಗೆ ಅನುಗುಣವಾಗಿ ತಜ್ಞ ಕೃಷಿ ಸಲಹೆ' : 
                   'आपकी विशिष्ट आवश्यकताओं के अनुरूप विशेषज्ञ कृषि सलाह'}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 'auto' }}>
                  <Button 
                    variant="text" 
                    color="primary" 
                    endIcon={<ArrowForwardIcon />}
                    sx={{ fontWeight: 600, textTransform: 'none' }}
                  >
                    {language === 'en' ? 'Learn more' : language === 'kn' ? 'ಇನ್ನಷ್ಟು ತಿಳಿಯಿರಿ' : 'अधिक जानें'}
                  </Button>
                </Box>
              </Box>
            </Box>
          </Grid>
          
          {/* Service 2: Dairy Farming - New Design with Image */}
          <Grid item xs={12} md={6}>
            <Box
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                borderRadius: 2,
                bgcolor: 'white',
                boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                overflow: 'hidden',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 10px 20px rgba(0,0,0,0.1)'
                }
              }}
            >
              <Box
                sx={{
                  height: '200px',
                  width: '100%',
                  position: 'relative',
                  overflow: 'hidden'
                }}
              >
                <Box
                  component="img"
                  src="/darvi-images/dairy1.jpg"
                  alt="Dairy Farming"
                  sx={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    transition: 'transform 0.5s ease',
                    '&:hover': {
                      transform: 'scale(1.05)'
                    }
                  }}
                />
              </Box>
              
              <Box sx={{ p: 3 }}>
                <Typography variant="h5" component="h3" gutterBottom fontWeight={600} sx={{ textAlign: 'left' }}>
                  {language === 'en' ? 'Dairy Farming' : language === 'kn' ? 'ಹೈನುಗಾರಿಕೆ' : 'डेयरी फार्मिंग'}
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'left', mb: 2 }}>
                  {language === 'en' ? 'Modern dairy management with high-yield breeds and advanced practices' : 
                   language === 'kn' ? 'ಹೆಚ್ಚಿನ ಇಳುವರಿಯ ತಳಿಗಳು ಮತ್ತು ಸುಧಾರಿತ ಅಭ್ಯಾಸಗಳೊಂದಿಗೆ ಆಧುನಿಕ ಹೈನುಗಾರಿಕೆ ನಿರ್ವಹಣೆ' : 
                   'उच्च उपज वाले नस्लों और उन्नत प्रथाओं के साथ आधुनिक डेयरी प्रबंधन'}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 'auto' }}>
                  <Button 
                    variant="text" 
                    color="primary" 
                    endIcon={<ArrowForwardIcon />}
                    sx={{ fontWeight: 600, textTransform: 'none' }}
                  >
                    {language === 'en' ? 'Learn more' : language === 'kn' ? 'ಇನ್ನಷ್ಟು ತಿಳಿಯಿರಿ' : 'अधिक जानें'}
                  </Button>
                </Box>
              </Box>
            </Box>
          </Grid>
          
          {/* Service 3: Farmland Services - New Design with Image */}
          <Grid item xs={12} md={6}>
            <Box
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                borderRadius: 2,
                bgcolor: 'white',
                boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                overflow: 'hidden',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 10px 20px rgba(0,0,0,0.1)'
                }
              }}
            >
              <Box
                sx={{
                  height: '200px',
                  width: '100%',
                  position: 'relative',
                  overflow: 'hidden'
                }}
              >
                <Box
                  component="img"
                  src="/darvi-images/field1.png"
                  alt="Farmland Services"
                  sx={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    transition: 'transform 0.5s ease',
                    '&:hover': {
                      transform: 'scale(1.05)'
                    }
                  }}
                />
              </Box>
              
              <Box sx={{ p: 3 }}>
                <Typography variant="h5" component="h3" gutterBottom fontWeight={600} sx={{ textAlign: 'left' }}>
                  {language === 'en' ? 'Farmland Services' : language === 'kn' ? 'ಕೃಷಿ ಭೂಮಿ ಸೇವೆಗಳು' : 'कृषि भूमि सेवाएं'}
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'left', mb: 2 }}>
                  {language === 'en' ? 'Premium agricultural lands with excellent soil quality and water access' : 
                   language === 'kn' ? 'ಅತ್ಯುತ್ತಮ ಮಣ್ಣಿನ ಗುಣಮಟ್ಟ ಮತ್ತು ನೀರಿನ ಪ್ರವೇಶದೊಂದಿಗೆ ಪ್ರೀಮಿಯಂ ಕೃಷಿ ಭೂಮಿಗಳು' : 
                   'उत्कृष्ट मिट्टी की गुणवत्ता और पानी की पहुंच के साथ प्रीमियम कृषि भूमि'}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 'auto' }}>
                  <Button 
                    variant="text" 
                    color="primary" 
                    endIcon={<ArrowForwardIcon />}
                    sx={{ fontWeight: 600, textTransform: 'none' }}
                  >
                    {language === 'en' ? 'Learn more' : language === 'kn' ? 'ಇನ್ನಷ್ಟು ತಿಳಿಯಿರಿ' : 'अधिक जानें'}
                  </Button>
                </Box>
              </Box>
            </Box>
          </Grid>
          
          {/* Service 4: Site Visit - New Design with Image */}
          <Grid item xs={12} md={6}>
            <Box
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                borderRadius: 2,
                bgcolor: 'white',
                boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                overflow: 'hidden',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 10px 20px rgba(0,0,0,0.1)'
                }
              }}
            >
              <Box
                sx={{
                  height: '200px',
                  width: '100%',
                  position: 'relative',
                  overflow: 'hidden',
                  bgcolor: 'rgba(46, 125, 50, 0.1)'
                }}
              >
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    bgcolor: 'rgba(46, 125, 50, 0.1)'
                  }}
                >
                  <LocationOnIcon sx={{ fontSize: 80, color: theme.palette.primary.main, opacity: 0.7 }} />
                </Box>
              </Box>
              
              <Box sx={{ p: 3 }}>
                <Typography variant="h5" component="h3" gutterBottom fontWeight={600} sx={{ textAlign: 'left' }}>
                  {language === 'en' ? 'Site Visit' : language === 'kn' ? 'ಸ್ಥಳ ಭೇಟಿ' : 'साइट विजिट'}
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'left', mb: 2 }}>
                  {language === 'en' ? 'On-site evaluation and assessment of your farmland' : 
                   language === 'kn' ? 'ನಿಮ್ಮ ಕೃಷಿ ಭೂಮಿಯ ಆನ್-ಸೈಟ್ ಮೌಲ್ಯಮಾಪನ ಮತ್ತು ಮೌಲ್ಯಮಾಪನ' : 
                   'आपकी कृषि भूमि का ऑन-साइट मूल्यांकन और आकलन'}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 'auto' }}>
                  <Button 
                    variant="text" 
                    color="primary" 
                    endIcon={<ArrowForwardIcon />}
                    sx={{ fontWeight: 600, textTransform: 'none' }}
                  >
                    {language === 'en' ? 'Learn more' : language === 'kn' ? 'ಇನ್ನಷ್ಟು ತಿಳಿಯಿರಿ' : 'अधिक जानें'}
                  </Button>
                </Box>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </SectionContainer>

      {/* Farmland and IoT Sections Side by Side */}
      <Box sx={{ py: 6, bgcolor: '#F8F9FA' }}>
        <Container maxWidth="lg">
          <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
            {/* Farmland Introduction Section */}
            <Grid item xs={12} sm={6} md={6}>
              <Card
                elevation={3}
                sx={{
                  height: '100%',
                  borderRadius: { xs: '12px', sm: '16px' },
                  transition: 'all 0.3s ease',
                  background: 'linear-gradient(135deg, #1B4C35 0%, #2E7D32 100%)',
                  color: 'white',
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'url(/darvi-images/field1.png)',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    opacity: 0.1,
                    zIndex: 0
                  },
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 16px 40px rgba(27,76,53,0.3)',
                    '&::before': {
                      opacity: 0.15
                    }
                  }
                }}
              >
                <Box sx={{
                  p: { xs: 2, sm: 2.5, md: 3 },
                  textAlign: 'center',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  position: 'relative',
                  zIndex: 1
                }}>
                  <Box sx={{ mb: { xs: 1, sm: 1.5, md: 2 } }}>
                    <TerrainIcon sx={{
                      fontSize: { xs: 36, sm: 40, md: 48 },
                      color: 'white',
                      filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'
                    }} />
                  </Box>
                  <Typography
                    variant="h5"
                    gutterBottom
                    fontWeight={700}
                    sx={{
                      fontSize: { xs: '1.1rem', sm: '1.25rem', md: '1.5rem' },
                      lineHeight: 1.2,
                      mb: { xs: 0.75, sm: 1, md: 1.5 },
                      textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                    }}
                  >
                    {language === 'en' ? 'Explore Farmlands' : language === 'kn' ? 'ಕೃಷಿ ಭೂಮಿಯನ್ನು ಅನ್ವೇಷಿಸಿ' : 'कृषि भूमि का अन्वेषण करें'}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      mb: { xs: 2, sm: 3 },
                      flexGrow: 1,
                      fontSize: { xs: '0.875rem', sm: '1rem' },
                      lineHeight: 1.6,
                      opacity: 0.95,
                      textShadow: '0 1px 2px rgba(0,0,0,0.2)'
                    }}
                  >
                    {language === 'en' ? 'Discover premium agricultural lands with excellent soil quality, water access, and strategic locations for your farming ventures.' : language === 'kn' ? 'ನಿಮ್ಮ ಕೃಷಿ ಉದ್ಯಮಗಳಿಗೆ ಅತ್ಯುತ್ತಮ ಮಣ್ಣಿನ ಗುಣಮಟ್ಟ, ನೀರಿನ ಪ್ರವೇಶ ಮತ್ತು ಕಾರ್ಯತಂತ್ರದ ಸ್ಥಳಗಳೊಂದಿಗೆ ಪ್ರೀಮಿಯಂ ಕೃಷಿ ಭೂಮಿಯನ್ನು ಅನ್ವೇಷಿಸಿ।' : 'अपने कृषि उद्यमों के लिए उत्कृष्ट मिट्टी की गुणवत्ता, पानी की पहुंच और रणनीतिक स्थानों के साथ प्रीमियम कृषि भूमि की खोज करें।'}
                  </Typography>
                  
                  {/* Farmland Overview Points */}
                  <Box sx={{ mb: 2, textAlign: 'left', width: '100%' }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1, color: 'white', textShadow: '0 1px 2px rgba(0,0,0,0.2)' }}>
                      {language === 'en' ? 'Farmland Highlights:' : language === 'kn' ? 'ಕೃಷಿ ಭೂಮಿ ಹೈಲೈಟ್ಸ್:' : 'कृषि भूमि हाइलाइट्स:'}
                    </Typography>
                    <Grid container spacing={1}>
                      {[
                        language === 'en' ? 'Premium soil quality for optimal crop growth' : language === 'kn' ? 'ಅನುಕೂಲಕರ ಬೆಳೆ ಬೆಳವಣಿಗೆಗೆ ಪ್ರೀಮಿಯಂ ಮಣ್ಣಿನ ಗುಣಮಟ್ಟ' : 'इष्टतम फसल विकास के लिए प्रीमियम मिट्टी की गुणवत्ता',
                        language === 'en' ? 'Reliable water sources and irrigation systems' : language === 'kn' ? 'ವಿಶ್ವಾಸಾರ್ಹ ನೀರಿನ ಮೂಲಗಳು ಮತ್ತು ನೀರಾವರಿ ವ್ಯವಸ್ಥೆಗಳು' : 'विश्वसनीय जल स्रोत और सिंचाई प्रणाली',
                        language === 'en' ? 'Strategic locations with good connectivity' : language === 'kn' ? 'ಉತ್ತಮ ಸಂಪರ್ಕದೊಂದಿಗೆ ಕಾರ್ಯತಂತ್ರದ ಸ್ಥಳಗಳು' : 'अच्छी कनेक्टिविटी के साथ रणनीतिक स्थान',
                        language === 'en' ? 'Legal documentation and clear titles' : language === 'kn' ? 'ಕಾನೂನು ದಾಖಲೆಗಳು ಮತ್ತು ಸ್ಪಷ್ಟ ಶೀರ್ಷಿಕೆಗಳು' : 'कानूनी दस्तावेज और स्पष्ट शीर्षक'
                      ].map((point, index) => (
                        <Grid item xs={12} key={index}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Box sx={{ 
                              width: 6, 
                              height: 6, 
                              borderRadius: '50%', 
                              bgcolor: 'white', 
                              mr: 1.5,
                              flexShrink: 0,
                              boxShadow: '0 1px 3px rgba(0,0,0,0.2)'
                            }} />
                            <Typography variant="body2" sx={{ 
                              fontSize: '0.85rem', 
                              color: 'white', 
                              opacity: 0.95,
                              textShadow: '0 1px 2px rgba(0,0,0,0.2)',
                              lineHeight: 1.4
                            }}>
                              {point}
                            </Typography>
                          </Box>
                        </Grid>
                      ))}
                    </Grid>
                  </Box>
                  <Button
                    variant="outlined"
                    onClick={() => window.open('https://darvigroupfarmlands.netlify.app/', '_blank')}
                    fullWidth
                    sx={{
                      borderColor: 'white',
                      color: 'white',
                      minHeight: { xs: '44px', sm: '40px' },
                      fontSize: { xs: '0.875rem', sm: '1rem' },
                      fontWeight: 600,
                      mb: 2,
                      borderRadius: 2,
                      textTransform: 'none',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        backgroundColor: 'rgba(255,255,255,0.15)',
                        borderColor: 'white',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.2)'
                      }
                    }}
                  >
                    {language === 'en' ? 'Click Here for Exploring' : language === 'kn' ? 'ಅನ್ವೇಷಣೆಗಾಗಿ ಇಲ್ಲಿ ಕ್ಲಿಕ್ ಮಾಡಿ' : 'अन्वेषण के लिए यहाँ क्लिक करें'}
                  </Button>
                  
                  {/* Farmland Contact Form */}
                  <FarmlandContactForm language={language} />
                </Box>
              </Card>
            </Grid>

            {/* IoT Management Preview */}
            <Grid item xs={12} sm={6} md={6}>
              <Card
                elevation={3}
                sx={{
                  height: '100%',
                  borderRadius: { xs: '12px', sm: '16px' },
                  transition: 'all 0.3s ease',
                  background: 'linear-gradient(135deg, #1B4C35 0%, #2E7D32 100%)',
                  color: 'white',
                  position: 'relative',
                  overflow: 'hidden',
                  cursor: 'pointer',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'url(/darvi-images/iot-device-agriculture.jpg)',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    opacity: 0.1,
                    zIndex: 0
                  },
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 16px 40px rgba(27,76,53,0.3)',
                    '&::before': {
                      opacity: 0.15
                    }
                  }
                }}
                onClick={() => setShowResearchPopup(true)}
              >
                <Box sx={{
                  p: { xs: 2, sm: 2.5, md: 3 },
                  textAlign: 'center',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  position: 'relative',
                  zIndex: 1
                }}>
                  <Box sx={{ mb: { xs: 1, sm: 1.5, md: 2 } }}>
                    <ScienceIcon sx={{
                      fontSize: { xs: 36, sm: 40, md: 48 },
                      color: 'white',
                      filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'
                    }} />
                  </Box>
                  <Typography
                    variant="h5"
                    gutterBottom
                    fontWeight={700}
                    sx={{
                      fontSize: { xs: '1.1rem', sm: '1.25rem', md: '1.5rem' },
                      lineHeight: 1.2,
                      mb: { xs: 1, sm: 1.5, md: 2 },
                      textAlign: 'center',
                      textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                    }}
                  >
                    {iotFeaturesContent.title[language as keyof typeof iotFeaturesContent.title] || iotFeaturesContent.title.en}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      mb: { xs: 2, sm: 3 },
                      fontSize: { xs: '0.875rem', sm: '1rem' },
                      lineHeight: 1.6,
                      opacity: 0.95,
                      textAlign: 'center',
                      textShadow: '0 1px 2px rgba(0,0,0,0.2)'
                    }}
                  >
                    {iotFeaturesContent.subtitle[language as keyof typeof iotFeaturesContent.subtitle] || iotFeaturesContent.subtitle.en}
                  </Typography>
                  
                  {/* Dairy Farming Overview */}
                  <Card
                    sx={{
                      mb: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.1)',
                      backdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255, 255, 255, 0.2)',
                      borderRadius: 2,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(255, 255, 255, 0.15)',
                        transform: 'translateY(-2px)'
                      }
                    }}
                  >
                    <Box sx={{ p: 2 }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1, color: 'white', textAlign: 'center', textShadow: '0 1px 2px rgba(0,0,0,0.2)' }}>
                        {language === 'en' ? 'Dairy Farming Excellence' : language === 'kn' ? 'ಡೇರಿ ಫಾರ್ಮಿಂಗ್ ಎಕ್ಸಲೆನ್ಸ್' : 'डेयरी फार्मिंग एक्सीलेंस'}
                      </Typography>
                      <Grid container spacing={1}>
                        {[
                          language === 'en' ? 'Modern facilities with advanced milking systems' : language === 'kn' ? 'ಆಧುನಿಕ ಹಾಲು ಕರೆಯುವ ವ್ಯವಸ್ಥೆಗಳೊಂದಿಗೆ ಆಧುನಿಕ ಸೌಲಭ್ಯಗಳು' : 'उन्नत दूध निकालने की प्रणालियों के साथ आधुनिक सुविधाएं',
                          language === 'en' ? 'High-quality feed and nutrition management' : language === 'kn' ? 'ಉನ್ನತ ಗುಣಮಟ್ಟದ ಆಹಾರ ಮತ್ತು ಪೋಷಣೆ ನಿರ್ವಹಣೆ' : 'उच्च गुणवत्ता वाला चारा और पोषण प्रबंधन',
                          language === 'en' ? 'Veterinary support and animal welfare' : language === 'kn' ? 'ಪಶುವೈದ್ಯಕೀಯ ಬೆಂಬಲ ಮತ್ತು ಪ್ರಾಣಿ ಕಲ್ಯಾಣ' : 'पशु चिकित्सा सहायता और पशु कल्याण',
                          language === 'en' ? 'Sustainable and eco-friendly practices' : language === 'kn' ? 'ಸುಸ್ಥಿರ ಮತ್ತು ಪರಿಸರ-ಸ್ನೇಹಿ ಅಭ್ಯಾಸಗಳು' : 'टिकाऊ और पर्यावरण-अनुकूल प्रथाएं'
                        ].map((point, index) => (
                          <Grid item xs={12} key={index}>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Box sx={{ 
                                width: 6, 
                                height: 6, 
                                borderRadius: '50%', 
                                bgcolor: 'white', 
                                mr: 1.5,
                                flexShrink: 0,
                                boxShadow: '0 1px 3px rgba(0,0,0,0.2)'
                              }} />
                              <Typography variant="body2" sx={{ 
                                fontSize: '0.85rem', 
                                color: 'white', 
                                opacity: 0.95,
                                textShadow: '0 1px 2px rgba(0,0,0,0.2)',
                                lineHeight: 1.4
                              }}>
                                {point}
                              </Typography>
                            </Box>
                          </Grid>
                        ))}
                      </Grid>
                    </Box>
                  </Card>
                  
                  <Grid container spacing={{ xs: 1, sm: 2 }} sx={{ mt: 1, mb: 3 }}>
                    {iotFeatures.slice(0, 4).map((feature, index) => (
                      <Grid item xs={6} key={index}>
                        <Box
                          sx={{
                            bgcolor: 'rgba(255, 255, 255, 0.1)',
                            p: { xs: 1, sm: 1.5, md: 2 },
                            borderRadius: 2,
                            height: '100%',
                            transition: 'all 0.3s ease',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            textAlign: 'center',
                            backdropFilter: 'blur(10px)',
                            border: '1px solid rgba(255, 255, 255, 0.2)',
                            '&:hover': {
                              bgcolor: 'rgba(255, 255, 255, 0.15)',
                              transform: 'translateY(-5px)',
                              boxShadow: '0 8px 25px rgba(0,0,0,0.2)'
                            }
                          }}
                        >
                          <Box sx={{
                            color: 'white',
                            mb: 1,
                            fontSize: { xs: '1.25rem', sm: '1.5rem' },
                            filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'
                          }}>
                            {feature.icon}
                          </Box>
                          <Typography
                            variant="subtitle2"
                            sx={{
                              color: 'white',
                              fontWeight: 600,
                              fontSize: { xs: '0.8rem', sm: '0.9rem' },
                              lineHeight: 1.2,
                              textShadow: '0 1px 2px rgba(0,0,0,0.2)'
                            }}
                          >
                            {feature.title}
                          </Typography>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                  
                  <Box sx={{ mt: 'auto', textAlign: 'center' }}>
                    <Button
                      variant="outlined"
                      onClick={() => setShowResearchPopup(true)}
                      sx={{
                        borderColor: 'white',
                        color: 'white',
                        minHeight: { xs: '44px', sm: '40px' },
                        fontSize: { xs: '0.875rem', sm: '1rem' },
                        fontWeight: 600,
                        borderRadius: 2,
                        textTransform: 'none',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          backgroundColor: 'rgba(255,255,255,0.15)',
                          borderColor: 'white',
                          transform: 'translateY(-2px)',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.2)'
                        }
                      }}
                    >
                      {iotFeaturesContent.linkText[language as keyof typeof iotFeaturesContent.linkText] || iotFeaturesContent.linkText.en}
                    </Button>
                  </Box>
                </Box>
              </Card>
            </Grid>
          </Grid>
        </Container>
      </Box>



      {/* Gallery Section */}
      <SectionContainer
        title={<AnimatedText text={galleryContent.title[language as keyof typeof galleryContent.title] || galleryContent.title.en} animation="slideUp" delay={0.2} duration={0.8} variant="h3" />}
        subtitle={galleryContent.subtitle[language as keyof typeof galleryContent.subtitle] || galleryContent.subtitle.en}
        bgColor="#f5f5f5"
        paddingTop={8}
        paddingBottom={8}
      >
        <Box sx={{
          mt: { xs: 2, sm: 3, md: 4 },
          maxWidth: '900px',
          mx: 'auto',
          height: { xs: '200px', sm: '250px', md: '400px', lg: '500px' },
          position: 'relative',
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)',
          borderRadius: { xs: 1, sm: 1.5, md: 2 },
          overflow: 'hidden'
        }}>
          <ImageSlider
            slides={galleryImages.map(image => ({
              image: image.src,
              title: image.title,
              description: '',
              url: ''
            }))}
            height="100%"
            showArrows={true}
            showDots={true}
            showCaption={true}
            borderRadius={0}
            effect="fade"
            autoPlayInterval={3000}
          />
        </Box>
      </SectionContainer>

      {/* Contact Preview */}
      <SectionContainer
        title={<AnimatedText text={contactContent.title[language as keyof typeof contactContent.title] || contactContent.title.en} animation="fadeIn" delay={0.2} duration={0.8} variant="h3" />}
        subtitle={contactContent.subtitle[language as keyof typeof contactContent.subtitle] || contactContent.subtitle.en}
        linkTo="/contact"
        linkText={t.common.contactUs}
        paddingTop={8}
        paddingBottom={8}
      >
        <Grid container spacing={{ xs: 2, sm: 3, md: 4 }} sx={{ mt: { xs: 2, sm: 3, md: 4 }, justifyContent: 'center' }}>
          {contactInfo.map((info, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  textAlign: 'center',
                  p: { xs: 2, sm: 2.5, md: 3 },
                  borderRadius: { xs: 2, sm: 3, md: 4 },
                  bgcolor: 'rgba(46, 125, 50, 0.05)',
                  height: '100%',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    bgcolor: 'rgba(46, 125, 50, 0.1)',
                    transform: 'translateY(-5px)',
                  }
                }}
              >
                <Box sx={{ mb: { xs: 1, sm: 1.5, md: 2 } }}>
                  {info.icon}
                </Box>
                <Typography
                  variant="h6"
                  gutterBottom
                  color="primary"
                  sx={{
                    fontWeight: 600,
                    fontSize: { xs: '1rem', sm: '1.1rem', md: '1.25rem' }
                  }}
                >
                  {info.title}
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: '0.8rem', sm: '0.875rem', md: '0.875rem' },
                    wordBreak: 'break-word'
                  }}
                >
                  {info.content}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>
      </SectionContainer>

      {/* Team Section */}
      <TeamSection />

      {/* About Section Preview */}
      <SectionContainer
        title={<AnimatedText text={language === 'en' ? "About Darvi Group" : language === 'kn' ? "ದಾರ್ವಿ ಗ್ರೂಪ್ ಬಗ್ಗೆ" : "दारवी ग्रुप के बारे में"} animation="slideUp" delay={0.2} duration={0.8} variant="h3" />}
        subtitle={language === 'en' ? "Your trusted partner in agricultural and forestry solutions since 2018" :
                language === 'kn' ? "2018 ರಿಂದ ಕೃಷಿ ಮತ್ತು ಅರಣ್ಯ ಪರಿಹಾರಗಳಲ್ಲಿ ನಿಮ್ಮ ವಿಶ್ವಾಸಾರ್ಹ ಪಾಲುದಾರ" :
                "2018 से कृषि और वानिकी समाधानों में आपका विश्वसनीय साथी"}
        linkTo="/about"
        linkText={t.common.learnMore}
        paddingTop={8}
        paddingBottom={8}
      >
        <Grid container spacing={{ xs: 2, sm: 3, md: 4 }} sx={{ mt: { xs: 2, sm: 3, md: 4 } }}>
          {values.map((value, index) => (
            <Grid item xs={6} sm={6} md={3} key={index}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  textAlign: 'center',
                  p: { xs: 2, sm: 2.5, md: 3 },
                  borderRadius: { xs: 2, sm: 3, md: 4 },
                  boxShadow: '0 8px 24px rgba(0, 0, 0, 0.05)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 16px 32px rgba(0, 0, 0, 0.1)',
                  }
                }}
              >
                <Box
                  sx={{
                    mb: { xs: 1, sm: 1.5, md: 2 },
                    p: { xs: 1.5, sm: 1.75, md: 2 },
                    borderRadius: '50%',
                    bgcolor: 'rgba(46, 125, 50, 0.1)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  {value.icon}
                </Box>
                <Typography
                  variant="h6"
                  gutterBottom
                  color="primary"
                  sx={{
                    fontWeight: 600,
                    fontSize: { xs: '0.9rem', sm: '1rem', md: '1.25rem' }
                  }}
                >
                  {value.title}
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: '0.75rem', sm: '0.8rem', md: '0.875rem' }
                  }}
                >
                  {value.description}
                </Typography>
              </Card>
            </Grid>
          ))}
        </Grid>
      </SectionContainer>

      {/* Testimonial Section */}
      <TestimonialSection />

      {/* Footer */}
      <Footer />
      
      {/* Research Center Popup */}
      <ResearchCenterPopup 
        open={showResearchPopup} 
        onClose={() => setShowResearchPopup(false)} 
      />
    </Box>
  );
};

export default HomePage;
