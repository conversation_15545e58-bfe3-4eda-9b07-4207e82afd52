/**
 * Production-Safe Logger Utility
 * Provides logging functionality that is completely silent in production
 * Only logs in development environment to prevent console output in production
 */

const isDevelopment = process.env.NODE_ENV === 'development';
const isDebugEnabled = process.env.DEBUG === 'true' || isDevelopment;

/**
 * Safe console.log - only logs in development
 */
const log = (...args) => {
  if (isDevelopment) {
    console.log(...args);
  }
};

/**
 * Safe console.warn - only logs in development
 */
const warn = (...args) => {
  if (isDevelopment) {
    console.warn(...args);
  }
};

/**
 * Safe console.error - only logs critical errors in development
 * In production, errors are still thrown but not logged to console
 */
const error = (...args) => {
  if (isDevelopment) {
    console.error(...args);
  }
};

/**
 * Debug logging - only when explicitly enabled
 */
const debug = (...args) => {
  if (isDebugEnabled) {
    console.debug(...args);
  }
};

/**
 * Info logging - only in development
 */
const info = (...args) => {
  if (isDevelopment) {
    console.info(...args);
  }
};

/**
 * Silent logger for production - all methods are no-ops
 */
const silentLogger = {
  log: () => {},
  warn: () => {},
  error: () => {},
  debug: () => {},
  info: () => {}
};

/**
 * Development logger - full console functionality
 */
const devLogger = {
  log,
  warn,
  error,
  debug,
  info
};

/**
 * Export appropriate logger based on environment
 */
const logger = isDevelopment ? devLogger : silentLogger;

/**
 * Structured logging for payment operations
 * Only logs in development, completely silent in production
 */
const logPaymentOperation = (operation, data = {}) => {
  if (!isDevelopment) return;
  
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [PAYMENT] ${operation}:`, data);
};

/**
 * Log configuration information - development only
 */
const logConfig = (configName, config = {}) => {
  if (!isDevelopment) return;
  
  console.log(`${configName} Configuration:`, config);
};

/**
 * Log transaction details - development only
 */
const logTransaction = (data, type = 'info') => {
  if (!isDevelopment) return;
  
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${type.toUpperCase()}]`, JSON.stringify(data, null, 2));
};

/**
 * Log hash operations - development only
 */
const logHashOperation = (operation, details = {}) => {
  if (!isDevelopment) return;
  
  console.log(`Hash ${operation}:`, details);
};

/**
 * Log errors with context - development only
 */
const logError = (message, context = {}) => {
  if (!isDevelopment) return;
  
  console.error(`❌ ${message}:`, context);
};

/**
 * Log warnings with context - development only
 */
const logWarning = (message, context = {}) => {
  if (!isDevelopment) return;
  
  console.warn(`⚠️ ${message}:`, context);
};

/**
 * Log success messages - development only
 */
const logSuccess = (message, context = {}) => {
  if (!isDevelopment) return;
  
  console.log(`✅ ${message}:`, context);
};

module.exports = {
  // Basic logger
  logger,
  
  // Individual methods
  log,
  warn,
  error,
  debug,
  info,
  
  // Specialized logging functions
  logPaymentOperation,
  logConfig,
  logTransaction,
  logHashOperation,
  logError,
  logWarning,
  logSuccess,
  
  // Environment checks
  isDevelopment,
  isDebugEnabled
};
