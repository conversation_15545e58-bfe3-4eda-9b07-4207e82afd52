import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogTitle,
  IconButton,
  Typography,
  Box,
  Grid,
  Button,
  Card,
  CardContent,
  Chip,
  Avatar,
  useTheme,
  useMediaQuery,
  keyframes
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import ScienceIcon from '@mui/icons-material/Science';
import BiotechIcon from '@mui/icons-material/Biotech';
import PsychologyIcon from '@mui/icons-material/Psychology';
import SchoolIcon from '@mui/icons-material/School';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import { Link } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';

// Animation keyframes
const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
`;

interface ResearchCenterPopupProps {
  open: boolean;
  onClose: () => void;
}

const ResearchCenterPopup: React.FC<ResearchCenterPopupProps> = ({ open, onClose }) => {
  const theme = useTheme();
  const { language } = useLanguage();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleClose = () => {
    setTimeout(onClose, 300);
  };

  const researchFeatures = [
    {
      icon: <BiotechIcon sx={{ fontSize: 24, color: 'white' }} />,
      title: language === 'en' ? 'Genetic Research' : 
             language === 'kn' ? 'ಜೆನೆಟಿಕ್ ಸಂಶೋಧನೆ' : 
             'जेनेटिक अनुसंधान',
      description: language === 'en' ? 'Advanced crop improvement' : 
                   language === 'kn' ? 'ಸುಧಾರಿತ ಬೆಳೆ ಸುಧಾರಣೆ' : 
                   'उन्नत फसल सुधार'
    },
    {
      icon: <PsychologyIcon sx={{ fontSize: 24, color: 'white' }} />,
      title: language === 'en' ? 'Sustainable Methods' : 
             language === 'kn' ? 'ಸುಸ್ಥಿರ ವಿಧಾನಗಳು' : 
             'स्थायी विधियां',
      description: language === 'en' ? 'Eco-friendly farming' : 
                   language === 'kn' ? 'ಪರಿಸರ ಸ್ನೇಹಿ ಕೃಷಿ' : 
                   'पर्यावरण अनुकूल खेती'
    },
    {
      icon: <SchoolIcon sx={{ fontSize: 24, color: 'white' }} />,
      title: language === 'en' ? 'Knowledge Transfer' : 
             language === 'kn' ? 'ಜ್ಞಾನ ವರ್ಗಾವಣೆ' : 
             'ज्ञान हस्तांतरण',
      description: language === 'en' ? 'Farmer education' : 
                   language === 'kn' ? 'ರೈತರ ಶಿಕ್ಷಣ' : 
                   'किसान शिक्षा'
    },
    {
      icon: <ScienceIcon sx={{ fontSize: 24, color: 'white' }} />,
      title: language === 'en' ? 'Innovation Hub' : 
             language === 'kn' ? 'ನವೀಕರಣ ಕೇಂದ್ರ' : 
             'नवाचार केंद्र',
      description: language === 'en' ? 'Cutting-edge solutions' : 
                   language === 'kn' ? 'ಅತ್ಯಾಧುನಿಕ ಪರಿಹಾರಗಳು' : 
                   'अत्याधुनिक समाधान'
    }
  ];

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          background: 'linear-gradient(135deg, #1B4C35 0%, #2E7D32 100%)',
          color: 'white',
          overflow: 'hidden',
          animation: `${fadeIn} 0.5s ease-out`,
          margin: { xs: 2, sm: 4 }, // Added responsive margins
          maxHeight: { xs: '90vh', sm: '80vh' }, // Added responsive maxHeight
          width: '100%'
        }
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: { xs: 2, sm: 3 },
          pb: 1,
          borderBottom: '1px solid rgba(255,255,255,0.1)'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar
            sx={{
              bgcolor: 'rgba(255,255,255,0.2)',
              width: { xs: 40, sm: 50 },
              height: { xs: 40, sm: 50 },
              animation: `${pulse} 2s infinite`
            }}
          >
            <ScienceIcon sx={{ fontSize: { xs: 20, sm: 24 } }} />
          </Avatar>
          <Typography
            variant={isMobile ? 'h6' : 'h5'}
            sx={{
              fontWeight: 700,
              background: 'linear-gradient(45deg, #fff, #e8f5e8)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}
          >
            {language === 'en' ? 'Research Center' :
             language === 'kn' ? 'ಸಂಶೋಧನಾ ಕೇಂದ್ರ' :
             'अनुसंधान केंद्र'}
          </Typography>
        </Box>
        <IconButton
          onClick={handleClose}
          sx={{
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255,255,255,0.1)'
            }
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent
        sx={{
          p: { xs: 2, sm: 3 },
          '&.MuiDialogContent-root': {
            paddingTop: 2
          }
        }}
      >
        {/* Description */}
        <Typography
          variant="body1"
          sx={{
            mb: 3,
            textAlign: 'center',
            lineHeight: 1.6,
            opacity: 0.9,
            fontSize: { xs: '0.9rem', sm: '1rem' }
          }}
        >
          {language === 'en' ? 
            'Discover cutting-edge agricultural research and sustainable farming solutions. Our research center focuses on genetic improvement, eco-friendly methods, and knowledge transfer to empower farmers.' :
           language === 'kn' ? 
            'ಅತ್ಯಾಧುನಿಕ ಕೃಷಿ ಸಂಶೋಧನೆ ಮತ್ತು ಸುಸ್ಥಿರ ಕೃಷಿ ಪರಿಹಾರಗಳನ್ನು ಅನ್ವೇಷಿಸಿ. ನಮ್ಮ ಸಂಶೋಧನಾ ಕೇಂದ್ರವು ಜೆನೆಟಿಕ್ ಸುಧಾರಣೆ, ಪರಿಸರ ಸ್ನೇಹಿ ವಿಧಾನಗಳು ಮತ್ತು ರೈತರನ್ನು ಸಬಲೀಕರಿಸಲು ಜ್ಞಾನ ವರ್ಗಾವಣೆಯ ಮೇಲೆ ಕೇಂದ್ರೀಕರಿಸಿದೆ.' :
            'अत्याधुनिक कृषि अनुसंधान और स्थायी खेती समाधानों की खोज करें। हमारा अनुसंधान केंद्र आनुवंशिक सुधार, पर्यावरण अनुकूल विधियों और किसानों को सशक्त बनाने के लिए ज्ञान हस्तांतरण पर केंद्रित है।'}
        </Typography>

        {/* Research Areas Card */}
        <Card
          sx={{
            mb: 3,
            bgcolor: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: 2
          }}
        >
          <CardContent sx={{ p: { xs: 2, sm: 2.5 } }}>
            <Typography
              variant="h6"
              sx={{
                mb: 2,
                fontWeight: 600,
                textAlign: 'center',
                fontSize: { xs: '1rem', sm: '1.1rem' }
              }}
            >
              {language === 'en' ? 'Research Areas' :
               language === 'kn' ? 'ಸಂಶೋಧನಾ ಕ್ಷೇತ್ರಗಳು' :
               'अनुसंधान क्षेत्र'}
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, justifyContent: 'center' }}>
              {['Sandalwood', 'Bamboo', 'Avocado'].map((item) => (
                <Chip
                  key={item}
                  label={item}
                  size="small"
                  sx={{
                    bgcolor: 'rgba(255, 255, 255, 0.2)',
                    color: 'white',
                    fontWeight: 500,
                    '&:hover': {
                      bgcolor: 'rgba(255, 255, 255, 0.3)'
                    }
                  }}
                />
              ))}
            </Box>
          </CardContent>
        </Card>

        {/* Research Features Grid */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          {researchFeatures.map((feature, index) => (
            <Grid item xs={6} key={index}>
              <Box
                sx={{
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  p: { xs: 1.5, sm: 2 },
                  borderRadius: 2,
                  height: '100%',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  textAlign: 'center',
                  cursor: 'pointer',
                  '&:hover': {
                    bgcolor: 'rgba(255, 255, 255, 0.15)',
                    transform: 'translateY(-5px)',
                    boxShadow: '0 8px 25px rgba(0,0,0,0.2)'
                  }
                }}
              >
                <Box sx={{ mb: 1 }}>
                  {feature.icon}
                </Box>
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontWeight: 600,
                    fontSize: { xs: '0.75rem', sm: '0.8rem' },
                    lineHeight: 1.2,
                    color: 'white',
                    mb: 0.5
                  }}
                >
                  {feature.title}
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: { xs: '0.65rem', sm: '0.7rem' },
                    opacity: 0.8,
                    lineHeight: 1.2
                  }}
                >
                  {feature.description}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>

        {/* Call to Action */}
        <Box sx={{ textAlign: 'center' }}>
          <Button
            variant="outlined"
            component={Link}
            to="/research-center"
            onClick={handleClose}
            endIcon={<ArrowForwardIcon />}
            sx={{
              borderColor: 'white',
              color: 'white',
              px: 4,
              py: 1.5,
              borderRadius: 28,
              fontSize: { xs: '0.875rem', sm: '1rem' },
              fontWeight: 600,
              textTransform: 'none',
              transition: 'all 0.3s ease',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.1)',
                borderColor: 'white',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.2)'
              }
            }}
          >
            {language === 'en' ? 'Explore Research Center' :
             language === 'kn' ? 'ಸಂಶೋಧನಾ ಕೇಂದ್ರವನ್ನು ಅನ್ವೇಷಿಸಿ' :
             'अनुसंधान केंद्र का अन्वेषण करें'}
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default ResearchCenterPopup;