import React from 'react';
import { Box, Container, Grid, Typography, <PERSON>, IconButton } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import FacebookIcon from '@mui/icons-material/Facebook';
import TwitterIcon from '@mui/icons-material/Twitter';
import InstagramIcon from '@mui/icons-material/Instagram';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import { useLanguage } from '../contexts/LanguageContext';
import translations from '../translations';
import footerContent from '../content/layout/footer.json';

const Footer = () => {
  const { language } = useLanguage();
  const t = translations[language as keyof typeof translations].common;
  return (
    <Box
      component="footer"
      sx={{
        bgcolor: '#1B4C35',
        color: 'white',
        py: 6,
        mt: 'auto',
      }}
    >
      <Container maxWidth="lg" sx={{ px: { xs: 2, sm: 3, md: 4 } }}>
        <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
          {/* Company Info */}
          <Grid item xs={6} sm={6} md={3}>
            <Typography variant="h6" gutterBottom sx={{
              fontWeight: 600,
              color: '#4CAF50',
              fontSize: { xs: '0.9rem', sm: '1rem', md: '1.25rem' }
            }}>
              Darvi Group
            </Typography>
            <Typography variant="body2" sx={{
              mb: 2,
              fontSize: { xs: '0.75rem', sm: '0.875rem' },
              display: { xs: 'none', sm: 'block' } // Hide description on mobile to save space
            }}>
              {footerContent.companyDescription[language as keyof typeof footerContent.companyDescription] || footerContent.companyDescription.en}
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              {footerContent.socialMedia.map((social, index) => {
                let icon;
                switch (social.platform) {
                  case 'Facebook':
                    icon = <FacebookIcon fontSize="small" />;
                    break;
                  case 'Twitter':
                    icon = <TwitterIcon fontSize="small" />;
                    break;
                  case 'Instagram':
                    icon = <InstagramIcon fontSize="small" />;
                    break;
                  case 'LinkedIn':
                    icon = <LinkedInIcon fontSize="small" />;
                    break;
                  default:
                    icon = <FacebookIcon fontSize="small" />;
                }
                return (
                  <IconButton
                    key={index}
                    component="a"
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    color="inherit"
                    aria-label={social.platform}
                    size="small"
                    sx={{ p: { xs: 0.5, sm: 1 } }}
                  >
                    {icon}
                  </IconButton>
                );
              })}
            </Box>
          </Grid>

          {/* Quick Links */}
          <Grid item xs={6} sm={6} md={3}>
            <Typography variant="h6" gutterBottom sx={{
              fontWeight: 600,
              color: '#4CAF50',
              fontSize: { xs: '0.9rem', sm: '1rem', md: '1.25rem' }
            }}>
              {language === 'en' ? footerContent.quickLinks.titleEn :
               language === 'kn' ? footerContent.quickLinks.titleKn :
               footerContent.quickLinks.titleHi}
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: { xs: 0.5, sm: 1 } }}>
              {footerContent.quickLinks.links.map((link, index) => (
                <Link
                  key={index}
                  component={RouterLink}
                  to={link.url}
                  color="inherit"
                  underline="hover"
                  sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                >
                  {link[language as keyof typeof link] || link.en}
                </Link>
              ))}
            </Box>
          </Grid>

          {/* Policies */}
          <Grid item xs={6} sm={6} md={3}>
            <Typography variant="h6" gutterBottom sx={{
              fontWeight: 600,
              color: '#4CAF50',
              fontSize: { xs: '0.9rem', sm: '1rem', md: '1.25rem' }
            }}>
              {language === 'en' ? footerContent.policies.titleEn :
               language === 'kn' ? footerContent.policies.titleKn :
               footerContent.policies.titleHi}
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: { xs: 0.5, sm: 1 } }}>
              {footerContent.policies.links.map((link, index) => (
                <Link
                  key={index}
                  component={RouterLink}
                  to={link.url}
                  color="inherit"
                  underline="hover"
                  sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                >
                  {link[language as keyof typeof link] || link.en}
                </Link>
              ))}
            </Box>
          </Grid>

          {/* Contact Info */}
          <Grid item xs={6} sm={6} md={3}>
            <Typography variant="h6" gutterBottom sx={{
              fontWeight: 600,
              color: '#4CAF50',
              fontSize: { xs: '0.9rem', sm: '1rem', md: '1.25rem' }
            }}>
              {t.contactUs}
            </Typography>
            <Typography variant="body2" sx={{
              mb: 0.5,
              fontSize: { xs: '0.75rem', sm: '0.875rem' }
            }}>
              {t.email}: <EMAIL>
            </Typography>
            <Typography variant="body2" sx={{
              mb: 0.5,
              fontSize: { xs: '0.75rem', sm: '0.875rem' }
            }}>
              {t.phone}: +91 9986890777
            </Typography>
            <Typography variant="body2" sx={{
              fontSize: { xs: '0.75rem', sm: '0.875rem' },
              display: { xs: 'none', sm: 'block' } // Hide full address on mobile to save space
            }}>
              {t.address}: Darvi Group, #2 Totad building, Arjun Vihar Gokul road Hubli, Karnataka
            </Typography>
            <Typography variant="body2" sx={{
              fontSize: { xs: '0.75rem', sm: '0.875rem' },
              display: { xs: 'block', sm: 'none' } // Show shortened address on mobile
            }}>
              {t.address}: Hubli, Karnataka
            </Typography>
          </Grid>
        </Grid>

        {/* Copyright */}
        <Box sx={{ mt: 4, pt: 2, borderTop: '1px solid rgba(255,255,255,0.1)' }}>
          <Typography variant="body2" align="center">
            {(footerContent.copyright[language as keyof typeof footerContent.copyright] || footerContent.copyright.en)
              .replace('{year}', new Date().getFullYear().toString())}
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;