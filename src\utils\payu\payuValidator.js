/**
 * PayU Payment Validator
 * Comprehensive validation for PayU payment parameters
 */

/**
 * Validate PayU payment request parameters
 * @param {Object} params - Payment parameters to validate
 * @returns {Object} Validation result with errors
 */
function validatePaymentRequest(params) {
  const errors = [];
  
  // Required field validation
  const requiredFields = {
    key: 'Merchant key',
    txnid: 'Transaction ID',
    amount: 'Amount',
    productinfo: 'Product information',
    firstname: 'Customer first name',
    email: 'Customer email',
    phone: 'Customer phone number'
  };
  
  for (const [field, label] of Object.entries(requiredFields)) {
    if (!params[field] || String(params[field]).trim() === '') {
      errors.push(`${label} is required`);
    }
  }
  
  // Specific field validations
  if (params.email && !isValidEmail(params.email)) {
    errors.push('Invalid email format');
  }
  
  if (params.phone && !isValidIndianMobile(params.phone)) {
    errors.push('Invalid Indian mobile number format');
  }
  
  if (params.amount) {
    const amount = parseFloat(params.amount);
    if (isNaN(amount) || amount <= 0) {
      errors.push('Amount must be a positive number');
    }
    if (amount > 200000) {
      errors.push('Amount cannot exceed ₹2,00,000');
    }
  }
  
  if (params.txnid && (params.txnid.length < 5 || params.txnid.length > 25)) {
    errors.push('Transaction ID must be between 5 and 25 characters');
  }
  
  if (params.productinfo && params.productinfo.length > 100) {
    errors.push('Product information cannot exceed 100 characters');
  }
  
  if (params.firstname && params.firstname.length > 60) {
    errors.push('First name cannot exceed 60 characters');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate PayU response parameters
 * @param {Object} response - PayU response to validate
 * @returns {Object} Validation result
 */
function validatePaymentResponse(response) {
  const errors = [];
  
  const requiredFields = ['txnid', 'status', 'amount', 'firstname', 'email', 'hash'];
  
  for (const field of requiredFields) {
    if (!response[field]) {
      errors.push(`Missing required response field: ${field}`);
    }
  }
  
  // Validate status
  const validStatuses = ['success', 'failure', 'pending', 'cancelled'];
  if (response.status && !validStatuses.includes(response.status.toLowerCase())) {
    errors.push('Invalid payment status');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Sanitize PayU payment parameters
 * @param {Object} params - Parameters to sanitize
 * @returns {Object} Sanitized parameters
 */
function sanitizePaymentParams(params) {
  const sanitized = {};
  
  // String fields that need trimming and length limits
  const stringFields = {
    key: 50,
    txnid: 25,
    productinfo: 100,
    firstname: 60,
    email: 100,
    phone: 15,
    address1: 255,
    city: 50,
    state: 50,
    country: 50,
    zipcode: 10,
    udf1: 255,
    udf2: 255,
    udf3: 255,
    udf4: 255,
    udf5: 255
  };
  
  for (const [field, maxLength] of Object.entries(stringFields)) {
    if (params[field]) {
      sanitized[field] = String(params[field])
        .trim()
        .replace(/[<>"'&]/g, '') // Remove potentially harmful characters
        .substring(0, maxLength);
    } else {
      sanitized[field] = '';
    }
  }
  
  // Amount sanitization
  if (params.amount) {
    const amount = parseFloat(params.amount);
    sanitized.amount = isNaN(amount) ? '0.00' : amount.toFixed(2);
  }
  
  // Phone number sanitization
  if (params.phone) {
    sanitized.phone = params.phone.replace(/\D/g, '').substring(0, 10);
  }
  
  // Email sanitization
  if (params.email) {
    sanitized.email = params.email.toLowerCase().trim();
  }
  
  return sanitized;
}

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} True if valid
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
}

/**
 * Validate Indian mobile number
 * @param {string} phone - Phone number to validate
 * @returns {boolean} True if valid
 */
function isValidIndianMobile(phone) {
  const cleanPhone = phone.replace(/\D/g, '');
  // Indian mobile numbers: 10 digits starting with 6, 7, 8, or 9
  return /^[6-9]\d{9}$/.test(cleanPhone);
}

/**
 * Validate PayU merchant credentials
 * @param {Object} credentials - Merchant credentials
 * @returns {Object} Validation result
 */
function validateMerchantCredentials(credentials) {
  const errors = [];
  
  if (!credentials.key || credentials.key.length < 5) {
    errors.push('Invalid merchant key');
  }
  
  if (!credentials.salt || credentials.salt.length < 5) {
    errors.push('Invalid merchant salt');
  }
  
  if (credentials.mid && credentials.mid.length < 5) {
    errors.push('Invalid merchant ID (MID)');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate transaction amount against business rules
 * @param {number} amount - Amount to validate
 * @param {Object} rules - Business rules
 * @returns {Object} Validation result
 */
function validateTransactionAmount(amount, rules = {}) {
  const errors = [];
  const numAmount = parseFloat(amount);
  
  if (isNaN(numAmount) || numAmount <= 0) {
    errors.push('Amount must be a positive number');
    return { isValid: false, errors };
  }
  
  const minAmount = rules.minAmount || 1;
  const maxAmount = rules.maxAmount || 200000;
  
  if (numAmount < minAmount) {
    errors.push(`Minimum amount is ₹${minAmount}`);
  }
  
  if (numAmount > maxAmount) {
    errors.push(`Maximum amount is ₹${maxAmount}`);
  }
  
  // Check for specific amount if required (e.g., fixed registration fee)
  if (rules.fixedAmount && numAmount !== rules.fixedAmount) {
    errors.push(`Amount must be exactly ₹${rules.fixedAmount}`);
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Generate unique transaction ID
 * @param {string} prefix - Prefix for transaction ID
 * @returns {string} Unique transaction ID
 */
function generateTransactionId(prefix = 'DARVI') {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 10000);
  return `${prefix}_${timestamp}_${random}`;
}

module.exports = {
  validatePaymentRequest,
  validatePaymentResponse,
  sanitizePaymentParams,
  isValidEmail,
  isValidIndianMobile,
  validateMerchantCredentials,
  validateTransactionAmount,
  generateTransactionId
};
