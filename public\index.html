<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/darvi-icon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Darvi Group - Agricultural and Forestry Solutions"
    />
    <!-- Content Security Policy for PayU integration and Netlify CMS (Development) -->
    <meta http-equiv="Content-Security-Policy" content="
      default-src 'self';
      script-src 'self' 'unsafe-inline' 'unsafe-eval' https://secure.payu.in https://test.payu.in https://*.payu.in https://www.googletagmanager.com https://www.google-analytics.com https://identity.netlify.com https://unpkg.com https://cdn.jsdelivr.net https://*.netlify.com https://*.netlify.app https://*.cloudinary.com https://api.cloudinary.com https://darvigroup.in;
      style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com https://cdn.jsdelivr.net;
      font-src 'self' https://fonts.gstatic.com https://unpkg.com https://cdn.jsdelivr.net;
      img-src 'self' data: https: blob: https://*.cloudinary.com https://darvigroup.in;
      connect-src 'self' https://darvigroup.in https://secure.payu.in https://test.payu.in https://*.payu.in https://info.payu.in https://script.google.com https://script.googleusercontent.com https://www.google-analytics.com https://analytics.google.com https://formsubmit.co https://api.emailjs.com https://api.web3forms.com https://api.netlify.com https://*.netlify.com https://*.netlify.app https://api.github.com https://uploads.github.com https://generativelanguage.googleapis.com https://api.weatherapi.com http://localhost:* ws://localhost:* https://*.cloudinary.com https://api.cloudinary.com;
      frame-src 'self' https://secure.payu.in https://test.payu.in https://*.payu.in https://*.netlify.com https://*.netlify.app;
      object-src 'none';
      base-uri 'self';
      form-action 'self' https://secure.payu.in/_payment https://test.payu.in/_payment https://secure.payu.in https://test.payu.in https://*.payu.in https://api.netlify.com;
    " />
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-YCXB3HK1NB"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-YCXB3HK1NB');
    </script>
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/darvi-logo.png" />

    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400;500;600;700&display=swap" rel="stylesheet">

    <title>Darvi Group</title>
    <script src="https://identity.netlify.com/v1/netlify-identity-widget.js"></script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>

    <!-- Hidden form for Netlify Forms -->
    <form name="farmland-contact" netlify netlify-honeypot="bot-field" hidden>
      <input type="text" name="email" />
      <input type="text" name="contactNumber" />
      <input type="text" name="message" />
      <input type="text" name="subject" />
      <input type="text" name="timestamp" />
    </form>

    <!-- Hidden form for farmland inquiry -->
    <form name="farmland-inquiry" netlify netlify-honeypot="bot-field" hidden>
      <input type="text" name="email" />
      <input type="text" name="contactNumber" />
      <input type="text" name="message" />
      <input type="text" name="subject" />
      <input type="text" name="timestamp" />
    </form>

    <div id="root"></div>

    <script>
      if (window.netlifyIdentity) {
        window.netlifyIdentity.on("init", user => {
          if (!user) {
            window.netlifyIdentity.on("login", () => {
              document.location.href = "/admin/";
            });
          }
        });
      }
    </script>
  </body>
</html>
