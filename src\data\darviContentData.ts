// This file contains the content data for Darvi Group
// Instead of importing from a text file, we'll define it directly as a JavaScript object

import config from '../config';

export const darviContentData = {
  companyInfo: {
    name: "Darvi Group",
    founded: "2018",
    location: "#2 Totad building, <PERSON><PERSON><PERSON> Gokul road Hubli, Karnataka 580030, India",
    contact: config.companyContact,
    email: config.contactEmail,
    website: "https://darvigroup.in",
    store: "https://mybillbook.in/store/darvi_group",
    iotPortal: "https://iot.haegl.in",
    workingHours: "Monday - Saturday, 9:00 AM - 6:00 PM",
    gstNumber: config.companyGST // GST number from config
  },

  team: [
    {
      name: "<PERSON><PERSON><PERSON>",
      position: "CEO",
      image: "/darvi_images/teams/nithinkumar.png",
      education: "BSc (Forestry)",
      specialization: "Sustainable Agriculture & Forestry",
      experience: "Forestry and Sustainable Agriculture",
      shortDescription: "Passionate advocate for sustainable agriculture and forestry practices.",
      fullDescription: "As a passionate advocate for sustainable agriculture and a seasoned professional in forestry, I have dedicated my career to transforming the way we approach farming. I am the CEO and Founder of Darvi Group, where we collaborate closely with farmers to implement cutting-edge cropping models and sustainable practices."
    },
    {
      name: "Sachinkumar",
      position: "Co-founder",
      image: "/darvi_images/teams/sachinkumar.png",
      education: "Environmental Studies, Geography and Commercial Forestry",
      specialization: "Sandalwood Cultivation and Land Development",
      experience: "Different commercial forest crops. Managed farmland Agroforestry model",
      shortDescription: "Expert in commercial forestry and agroforestry models."
    },
    {
      name: "Naveenkumar",
      position: "Marketing Head",
      image: "/darvi_images/teams/naveenkumar.png",
      education: "B.Sc Computer Application, MBA (Marketing)",
      specialization: "Marketing and Operations",
      experience: "Integrated Application on Security Systems",
      shortDescription: "Strategic marketing professional with expertise in agricultural solutions."
    },
    {
      name: "Shareeshail B",
      position: "Technical Head",
      image: "/darvi_images/teams/shareeshail.png",
      education: "BSc & MSc (Forestry)",
      specialization: "Forest Product and Utilization (Essential Oil Extraction and Analysis)",
      experience: "Different commercial forest crops, Soil and Crop health monitoring",
      currentProject: "Sandalwood host, growth, size and yield based on different soil and climatic conditions",
      shortDescription: "Forestry expert specializing in crop health and agroforestry models."
    },
    {
      name: "Sushma TM",
      position: "Technical Head",
      image: "/darvi_images/teams/sushma.png",
      education: "BSc (Horticulture), MSc (Agriculture)",
      specialization: "Horticulture and Agriculture",
      experience: "Technical Assistant in Karnataka Horticulture Department, worked on different commercial Horticulture crops",
      currentProject: "Effectiveness of organic fertilizers on crop growth, health, yield and quality",
      shortDescription: "Horticulture specialist focused on organic farming practices."
    }
  ],

  missionValues: {
    mission: "Revolutionizing agricultural practices in India through sustainable solutions",
    values: ["Sustainable Agriculture", "Community Support", "Innovation", "Growth"],
    commitment: "Empowering farmers with knowledge, technology, and quality products"
  },

  products: [
    {
      name: "Genex",
      benefits: "Enhances soil fertility, improves crop yield, balances soil pH",
      bestFor: "All types of crops, especially cereals and vegetables",
      application: "Mix 5ml per liter of water, apply weekly",
      results: "20-30% increase in yield, healthier plants, improved root development",
      composition: "Organic nutrients, beneficial microorganisms, natural growth promoters"
    },
    {
      name: "Neem",
      benefits: "Natural pest control, organic farming solution, repels harmful insects",
      bestFor: "All crops, especially effective for vegetables and fruit trees",
      application: "Mix 3-5ml per liter of water, spray bi-weekly",
      results: "Reduces pest attacks by 70-80%, safe for beneficial insects",
      composition: "Pure neem extract, natural oils, organic emulsifiers"
    },
    {
      name: "Santica",
      benefits: "Improves plant health, increases productivity, strengthens immune system",
      bestFor: "Fruit trees, flowering plants, and high-value crops",
      application: "Mix 4ml per liter of water, apply every 15 days",
      results: "Enhanced flowering, better fruit quality, resistance to diseases",
      composition: "Plant extracts, essential nutrients, immunity boosters"
    }
  ],

  cropSolutions: [
    {
      crop: "Rice/Paddy",
      problems: ["Blast disease", "bacterial leaf blight", "stem borers"],
      recommendedProducts: ["Genex for soil health", "Neem for pest control"],
      bestPractices: ["Proper water management", "timely transplanting", "balanced fertilization"],
      iotSolutions: ["Water level sensors", "automated irrigation control"]
    },
    {
      crop: "Wheat",
      problems: ["Rust", "powdery mildew", "aphids"],
      recommendedProducts: ["Genex for root development", "Santica for disease resistance"],
      bestPractices: ["Timely sowing", "proper spacing", "adequate irrigation"],
      iotSolutions: ["Soil moisture sensors", "temperature monitoring"]
    },
    {
      crop: "Sugarcane",
      problems: ["Red rot", "smut", "borers"],
      recommendedProducts: ["Genex for soil fertility", "Neem for pest management"],
      bestPractices: ["Proper planting material", "trash mulching", "intercropping"],
      iotSolutions: ["Drip irrigation systems", "nutrient monitoring"]
    },
    {
      crop: "Cotton",
      problems: ["Bollworms", "whiteflies", "leaf curl virus"],
      recommendedProducts: ["Neem for pest control", "Santica for plant health"],
      bestPractices: ["Timely sowing", "proper spacing", "integrated pest management"],
      iotSolutions: ["Pest monitoring systems", "weather alerts"]
    },
    {
      crop: "Vegetables",
      problems: ["Fungal diseases", "insect pests", "nutrient deficiencies"],
      recommendedProducts: ["All three products in rotation"],
      bestPractices: ["Crop rotation", "mulching", "proper irrigation"],
      iotSolutions: ["Greenhouse automation", "humidity control"]
    },
    {
      crop: "Fruit Trees",
      problems: ["Fruit flies", "fungal infections", "nutrient deficiencies"],
      recommendedProducts: ["Santica for fruit quality", "Neem for pest control"],
      bestPractices: ["Regular pruning", "proper spacing", "balanced fertilization"],
      iotSolutions: ["Automated sprinkler systems", "fruit growth monitoring"]
    },
    {
      crop: "Sandlewood",
      problems: ["Root rot", "stem borers", "leaf-eating insects"],
      recommendedProducts: ["Neem for insect control", "Santica for overall health"],
      bestPractices: ["Proper spacing", "adequate watering", "companion planting"],
      specialCare: ["Protection from extreme weather", "regular monitoring for pests"]
    }
  ],

  agriculturalProblems: {
    pestManagement: [
      { pest: "Aphids", solution: "Use Neem at 5ml/liter, spray on affected areas focusing on undersides of leaves" },
      { pest: "Bollworms", solution: "Apply Neem at 6ml/liter during early stages of infestation" },
      { pest: "Stem Borers", solution: "Use Neem at 5ml/liter, focus on stem areas, repeat weekly" },
      { pest: "Whiteflies", solution: "Apply Neem at 4ml/liter, ensure complete coverage of plants" },
      { pest: "Fruit Flies", solution: "Use Neem at 5ml/liter, focus on fruits and surrounding areas" }
    ],

    diseaseManagement: [
      { disease: "Fungal Diseases", solution: "Apply Santica at 5ml/liter at first sign of infection" },
      { disease: "Bacterial Infections", solution: "Use Genex for soil health improvement to boost plant immunity" },
      { disease: "Viral Diseases", solution: "Focus on vector control with Neem, improve plant health with Santica" },
      { disease: "Root Rot", solution: "Apply Genex to improve soil conditions, ensure proper drainage" },
      { disease: "Powdery Mildew", solution: "Use Santica at 4ml/liter, ensure good air circulation around plants" }
    ],

    soilHealth: [
      { issue: "Poor Fertility", solution: "Apply Genex at 6ml/liter through irrigation system" },
      { issue: "Acidic Soil", solution: "Use Genex at 5ml/liter, which helps balance pH levels" },
      { issue: "Alkaline Soil", solution: "Apply Genex with organic matter to improve soil structure" },
      { issue: "Compacted Soil", solution: "Use Genex along with proper tillage practices" },
      { issue: "Sandy Soil", solution: "Apply Genex at 7ml/liter to improve water retention" }
    ],

    waterManagement: [
      { issue: "Drought Conditions", solution: "Apply Genex to improve soil water retention" },
      { issue: "Waterlogging", solution: "Improve drainage, apply Santica to strengthen root systems" },
      { issue: "Irrigation Efficiency", solution: "Use IoT sensors to optimize water usage" },
      { issue: "Water Quality Issues", solution: "Apply Genex to mitigate effects of poor water quality" },
      { issue: "Saline Water", solution: "Use Genex to reduce salt stress on plants" }
    ]
  },

  iotSolutions: {
    smartIrrigation: {
      features: ["Automated watering based on soil moisture", "weather forecasts"],
      benefits: ["Water savings up to 40%", "reduced labor", "optimal plant growth"],
      implementation: "Sensors installed in fields, connected to central control system",
      costEfficiency: "Pays for itself within 1-2 growing seasons through water savings"
    },

    cropMonitoring: {
      features: ["Real-time monitoring of crop health", "pest detection"],
      benefits: ["Early problem detection", "targeted interventions", "reduced crop loss"],
      implementation: "Camera systems, spectral analysis, AI-based detection",
      applications: "Works for all major crops, especially effective for high-value crops"
    },

    weatherStations: {
      features: ["Local weather monitoring", "frost/heat alerts", "rainfall tracking"],
      benefits: ["Better planning", "reduced weather-related losses"],
      implementation: "On-farm weather stations connected to farmer's mobile app",
      integration: "Works with other farm systems to automate responses to weather changes"
    },

    soilHealthMonitoring: {
      features: ["Continuous tracking of soil nutrients", "moisture", "pH"],
      benefits: ["Optimized fertilizer use", "improved crop health", "higher yields"],
      implementation: "In-ground sensors at various depths, wireless data transmission",
      dataAnalysis: "AI-powered recommendations for soil amendments"
    }
  },

  forestry: {
    sustainablePractices: [
      "Selective Harvesting: Maintaining forest health while extracting timber",
      "Reforestation: Fast-growing native species selection and planting techniques",
      "Forest Health: Monitoring and maintaining biodiversity and ecosystem balance",
      "Timber Management: Optimal spacing, pruning, and harvesting schedules"
    ],

    agroforestryModels: [
      "Alley Cropping: Growing crops between rows of trees",
      "Silvopasture: Combining trees with livestock grazing",
      "Forest Farming: Cultivating high-value crops under tree canopy",
      "Windbreaks: Using trees to protect crops and soil"
    ],

    commercialTimber: [
      "Teak: Management practices, spacing, pest control",
      "Eucalyptus: Fast-growing options, water management",
      "Sandalwood: Host plants, protection methods, harvesting timeline",
      "Bamboo: Varieties, management, harvesting techniques"
    ]
  },

  services: {
    farmerRegistration: {
      process: "Simple online form submission at darvigroup.com",
      fee: "₹500 one-time registration",
      benefits: ["Access to expert consultation", "product discounts", "IoT service trials"],
      support: "Assistance with government scheme registration, subsidy applications"
    },

    expertConsultation: {
      services: ["Soil testing", "crop planning", "pest management strategies"],
      process: "On-site visits by agricultural experts",
      customization: "Tailored advice based on specific farm conditions",
      followUp: "Regular check-ins and adjustment of recommendations"
    },

    trainingPrograms: {
      topics: ["Sustainable farming", "organic certification", "IoT implementation"],
      format: ["Hands-on workshops", "demonstration plots", "farmer field schools"],
      schedule: "Monthly programs at Darvi demonstration farm",
      specialPrograms: ["Women farmer empowerment", "youth in agriculture"]
    }
  },

  successStories: [
    {
      title: "Rice Farmer, Hubli Region",
      challenge: "Low yields due to poor soil health and pest issues",
      solution: "Implemented Genex for soil improvement, Neem for pest control",
      results: "35% yield increase, 40% reduction in chemical pesticide use",
      timeline: "Visible improvements within one growing season"
    },
    {
      title: "Mango Orchard, Karnataka",
      challenge: "Fruit quality issues, irregular flowering",
      solution: "Applied Santica throughout growing season, installed IoT irrigation",
      results: "45% increase in premium grade fruits, 30% water savings",
      roi: "Investment recovered within first harvest season"
    },
    {
      title: "Mixed Vegetable Farm",
      challenge: "Disease pressure, high input costs",
      solution: "Complete Darvi product system, soil health monitoring",
      results: "50% reduction in crop losses, 40% decrease in input costs",
      sustainability: "Achieved organic certification within two years"
    }
  ],

  seasonalRecommendations: {
    preMonsoon: [
      "Soil Preparation: Apply Genex to improve soil structure",
      "Pest Prevention: Preventative application of Neem",
      "Planning: Crop selection based on weather forecasts",
      "Infrastructure: Check and repair irrigation systems"
    ],

    monsoon: [
      "Disease Management: Regular application of Santica",
      "Water Management: Ensure proper drainage, prevent waterlogging",
      "Pest Control: Increased vigilance, timely Neem application",
      "Nutrient Management: Split application of Genex"
    ],

    postMonsoon: [
      "Soil Rejuvenation: Apply Genex to replenish nutrients",
      "Crop Protection: Preventative pest and disease management",
      "Irrigation: Transition to controlled irrigation schedules",
      "Planning: Prepare for winter crops"
    ],

    winter: [
      "Frost Protection: Apply Santica to improve cold tolerance",
      "Pest Management: Monitor for winter pests, targeted Neem application",
      "Soil Health: Maintain organic matter with Genex",
      "Water Conservation: Optimize irrigation with IoT monitoring"
    ],

    summer: [
      "Heat Stress Management: Apply Santica to strengthen plants",
      "Water Efficiency: Use IoT for precise irrigation timing",
      "Pest Vigilance: Monitor for summer pest outbreaks",
      "Soil Protection: Apply Genex to maintain soil moisture"
    ]
  }
};

// Helper function to get a formatted response about company information
export function getCompanyInfo() {
  const company = darviContentData.companyInfo;
  return `**About Darvi Group**

Darvi Group is a sustainable agricultural solutions company founded in ${company.founded}. We specialize in:

• Sustainable farming practices and agricultural consultation
• IoT-powered agriculture monitoring systems
• Premium agricultural products (Genex, Neem, Santica)
• Expert guidance on crop management and land optimization

Our mission is to empower farmers with modern techniques, sustainable practices, and expert guidance to achieve optimal yields while preserving natural resources.`;
}

// Helper function to get a formatted response about products
export function getProductsInfo() {
  return `**Our Products**

• **Genex**: Enhances soil fertility, improves crop yield
• **Neem**: Natural pest control, organic farming solution
• **Santica**: Improves plant health, increases productivity

Visit our store: https://mybillbook.in/store/darvi_group`;
}

// Helper function to get a formatted response about a specific product
export function getProductInfo(productName: string) {
  const product = darviContentData.products.find(p =>
    p.name.toLowerCase() === productName.toLowerCase()
  );

  if (!product) {
    return getProductsInfo();
  }

  return `**${product.name}**

• Benefits: ${product.benefits}
• Best for: ${product.bestFor}
• Application: ${product.application}`;
}

// Helper function to get a formatted response about pest management
export function getPestManagementInfo() {
  return `**Pest Management**

For effective pest control, we recommend our **Neem** product. It's a natural solution that:

• Controls a wide range of insect pests
• Is safe for beneficial insects
• Can be used on all types of crops
• Has no harmful residues

Application: Mix 3-5ml per liter of water and spray bi-weekly. For severe infestations, increase to weekly applications.`;
}

// Helper function to get a formatted response about disease management
export function getDiseaseManagementInfo() {
  return `**Disease Management**

For effective disease control, we recommend our **Santica** product. It:

• Strengthens plant immune systems
• Helps plants resist fungal and bacterial infections
• Improves overall plant health
• Enhances recovery from disease damage

Application: Mix 4ml per liter of water and apply every 15 days. For active infections, increase to weekly applications until symptoms subside.`;
}

// Helper function to get a formatted response about soil health
export function getSoilHealthInfo() {
  return `**Soil Health**

For improving soil health, we recommend our **Genex** product. It:

• Enhances soil fertility
• Balances soil pH
• Improves nutrient availability
• Promotes beneficial soil microorganisms
• Increases water retention capacity

Application: Mix 5ml per liter of water and apply weekly through irrigation or soil drenching.`;
}

// Helper function to get a formatted response about water management
export function getWaterManagementInfo() {
  return `**Water Management**

For water management issues, we recommend:

• **Genex** to improve soil water retention
• Our IoT-powered irrigation solutions for optimal water usage
• Smart sensors to monitor soil moisture levels

For drought conditions, apply Genex at 6ml per liter to improve water retention.
For waterlogging, improve drainage and apply Santica to strengthen root systems.`;
}

// Helper function to get a formatted response about a specific crop
export function getCropInfo(cropName: string) {
  // Handle 'paddy' as 'rice'
  const normalizedCropName = cropName.toLowerCase() === 'paddy' ? 'rice' : cropName.toLowerCase();

  const crop = darviContentData.cropSolutions.find(c =>
    c.crop.toLowerCase().includes(normalizedCropName)
  );

  if (!crop) {
    return `For ${cropName} cultivation, we recommend a combination of our products:

• **Genex** for soil health and fertility
• **Neem** for pest management
• **Santica** for disease resistance and plant health`;
  }

  return `**${crop.crop} Cultivation**

Common Problems: ${crop.problems.join(', ')}
Recommended Products: ${crop.recommendedProducts.join(', ')}
Best Practices: ${crop.bestPractices.join(', ')}
${crop.iotSolutions ? `IoT Solutions: ${crop.iotSolutions.join(', ')}` : ''}`;
}

// Helper function to get a general response
export function getGeneralResponse() {
  return `I can help you with information about:

• **Darvi Group** - Our company, mission, and services
• **Agricultural Products** - Genex, Neem, and Santica
• **Crop Management** - Solutions for various crops including rice, wheat, vegetables, etc.
• **Common Problems** - Pest management, disease control, soil health, irrigation
• **Our Team** - Information about our leadership and technical experts

Please feel free to ask about any of these topics.`;
}

// Helper function to get team information
export function getTeamInfo() {
  const team = darviContentData.team;
  return `**Our Team at Darvi Group**

${team.map(member => `• **${member.name}** - ${member.position}: ${member.shortDescription}`).join('\n')}

Our team combines expertise in forestry, agriculture, horticulture, and business management to provide comprehensive solutions for sustainable farming.`;
}

// Helper function to get information about a specific team member
export function getTeamMemberInfo(memberName: string) {
  const member = darviContentData.team.find(m =>
    m.name.toLowerCase().includes(memberName.toLowerCase())
  );

  if (!member) {
    return getTeamInfo();
  }

  return `**${member.name} - ${member.position}**

• Education: ${member.education}
• Specialization: ${member.specialization}
• Experience: ${member.experience}
${member.currentProject ? `• Current Project: ${member.currentProject}` : ''}

${member.fullDescription || member.shortDescription}`;
}

// Helper function to get contact information when response is not available
export function getContactInfo() {
  const company = darviContentData.companyInfo;
  return `I don't have specific information about that. For more details, please contact Darvi Group directly:

• Phone: ${company.contact}
• Email: ${company.email}
• Address: ${company.location}
• Working Hours: ${company.workingHours}

You can also visit our website at ${company.website} for more information.`;
}
