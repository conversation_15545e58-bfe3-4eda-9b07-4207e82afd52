/**
 * PayU Configuration Utility - EXAMPLE FILE
 * Copy this file to payuConfig.js and update with your actual credentials
 * Converted from PHP config.php to JavaScript for Netlify Functions
 * Handles environment variables and PayU configuration
 */

const { log, warn, error, logConfig, logWarning, logSuccess, logTransaction } = require('./productionLogger');

// PayU Configuration
const getPayUConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isTestMode = process.env.PAYU_TEST_MODE === 'true';

  // Determine which credentials to use based on availability and test mode
  let merchantKey, merchantId, salt;

  if (isTestMode || (!process.env.PAYU_MERCHANT_KEY && !isProduction)) {
    // Use test credentials - REPLACE WITH YOUR TEST CREDENTIALS
    merchantKey = process.env.PAYU_TEST_KEY || 'YOUR_TEST_KEY';
    merchantId = process.env.PAYU_TEST_MERCHANT_ID || 'YOUR_TEST_MERCHANT_ID';
    salt = process.env.PAYU_TEST_SALT || 'YOUR_TEST_SALT';
    log('🧪 Using PayU Test Credentials');
  } else {
    // Use production credentials
    merchantKey = process.env.PAYU_MERCHANT_KEY;
    merchantId = process.env.PAYU_MERCHANT_ID;
    salt = process.env.PAYU_SALT_256 || process.env.PAYU_SALT_32;
    log('🔐 Using PayU Production Credentials');
  }

  // Load configuration from environment variables
  const config = {
    // Merchant Details - Dynamic based on test mode and credential availability
    merchantKey: merchantKey,
    merchantId: merchantId,
    salt: salt,

    // Client Credentials - REPLACE WITH YOUR ACTUAL CREDENTIALS
    clientId: process.env.PAYU_CLIENT_ID || 'YOUR_CLIENT_ID',
    clientSecret: process.env.PAYU_CLIENT_SECRET || 'YOUR_CLIENT_SECRET',

    // Environment URLs
    baseUrl: process.env.PAYU_BASE_URL || 'https://secure.payu.in',
    paymentUrl: process.env.PAYU_PAYMENT_URL || 'https://secure.payu.in/_payment',

    // Test mode setting - can be forced even in production for testing
    testMode: isTestMode || (!process.env.PAYU_MERCHANT_KEY && !isProduction),

    // Site URLs - Dynamic based on environment
    siteUrl: isProduction ? 'https://your-domain.com' : 'http://localhost:3000',

    // PayU callback URLs - use Netlify routing for preprocessing
    successUrl: isProduction ? 'https://your-domain.com/payu/success' : 'http://localhost:3000/payu/success',
    failureUrl: isProduction ? 'https://your-domain.com/payu/failure' : 'http://localhost:3000/payu/failure',
    cancelUrl: isProduction ? 'https://your-domain.com/payu/cancel' : 'http://localhost:3000/payu/cancel'
  };

  // If test mode is enabled, use test URLs regardless of environment
  if (config.testMode) {
    config.paymentUrl = 'https://test.payu.in/_payment';
    config.verifyUrl = 'https://test.payu.in/merchant/postservice.php?form=2';
    log('⚠️ PayU Test Mode Enabled - Using test URLs');
  } else {
    config.verifyUrl = 'https://info.payu.in/merchant/postservice.php?form=2';
  }

  // Validate required credentials
  const requiredFields = ['merchantKey', 'merchantId', 'salt'];
  const missingFields = requiredFields.filter(field => !config[field]);

  if (missingFields.length > 0) {
    const errorMsg = `Missing required PayU credentials: ${missingFields.join(', ')}. Please set environment variables: ${missingFields.map(f => `PAYU_${f.toUpperCase()}`).join(', ')}`;
    error('❌ PayU Configuration Error:', errorMsg);
    throw new Error(errorMsg);
  }

  // Smart validation based on credential availability and environment
  if (process.env.NODE_ENV === 'development') {
    if (isProduction && !config.testMode) {
      // In production with test mode disabled, warn if using test credentials but don't fail
      if (config.merchantKey === 'YOUR_TEST_KEY' || config.merchantId === 'YOUR_TEST_MERCHANT_ID') {
        logWarning('Using test credentials in production. Consider setting production credentials or enabling test mode.');
        log('💡 To use production credentials, set: PAYU_MERCHANT_KEY, PAYU_MERCHANT_ID, PAYU_SALT_256');
        log('💡 To enable test mode in production, set: PAYU_TEST_MODE=true');
      } else {
        logSuccess('PayU Production Mode - Using live credentials');
      }
    } else if (isProduction && config.testMode) {
      log('⚠️ PayU Test Mode in Production - Using test credentials for testing');
    } else if (config.testMode) {
      log('🧪 PayU Test Mode - Using test credentials');
    }

    // Log configuration (without sensitive data) - only in development
    logConfig('PayU', {
      environment: isProduction ? 'production' : 'development',
      testMode: config.testMode,
      merchantId: config.merchantId,
      paymentUrl: config.paymentUrl,
      hasCredentials: !!(config.merchantKey && config.salt)
    });
  }

  return config;
};

/**
 * Generate secure hash for PayU according to official documentation
 * Format: sha512(key|txnid|amount|productinfo|firstname|email|udf1|udf2|udf3|udf4|udf5||||||SALT)
 */
const generatePayUHash = (key, txnid, amount, productinfo, firstname, email, salt, udf1 = '', udf2 = '', udf3 = '', udf4 = '', udf5 = '') => {
  const crypto = require('crypto');
  
  // Construct hash string exactly as per PayU documentation
  const hashString = `${key}|${txnid}|${amount}|${productinfo}|${firstname}|${email}|${udf1}|${udf2}|${udf3}|${udf4}|${udf5}||||||${salt}`;
  
  // Generate SHA512 hash and return in lowercase
  return crypto.createHash('sha512').update(hashString).digest('hex').toLowerCase();
};

// Transaction ID generation moved to payuValidator.js to avoid duplication

/**
 * Verify response hash from PayU (Reverse Hash)
 * Format: sha512(SALT|status||||||udf5|udf4|udf3|udf2|udf1|email|firstname|productinfo|amount|txnid|key)
 */
const verifyResponseHash = (key, txnid, amount, productinfo, firstname, email, status, salt, udf1 = '', udf2 = '', udf3 = '', udf4 = '', udf5 = '') => {
  const crypto = require('crypto');
  
  // Construct reverse hash string exactly as per PayU documentation
  const hashString = `${salt}|${status}||||||${udf5}|${udf4}|${udf3}|${udf2}|${udf1}|${email}|${firstname}|${productinfo}|${amount}|${txnid}|${key}`;
  
  // Generate SHA512 hash and return in lowercase
  return crypto.createHash('sha512').update(hashString).digest('hex').toLowerCase();
};

// Use the production-safe logTransaction from productionLogger

/**
 * Validate required environment variables
 */
const validateEnvironment = () => {
  const config = getPayUConfig();
  const errors = [];
  
  if (!config.merchantKey) {
    errors.push('PAYU_MERCHANT_KEY is required');
  }
  
  if (!config.salt) {
    errors.push('PAYU_SALT_256 or PAYU_SALT_32 is required');
  }
  
  if (!config.merchantId) {
    errors.push('PAYU_MERCHANT_ID is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    config
  };
};

module.exports = {
  getPayUConfig,
  generatePayUHash,
  verifyResponseHash,
  logTransaction,
  validateEnvironment
};
