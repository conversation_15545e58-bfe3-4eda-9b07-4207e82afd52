/**
 * Logger Utility for PayU Transactions
 * Handles transaction logging for debugging and audit purposes
 * Converted from PHP logging to JavaScript for Netlify Functions
 * Uses production-safe logging that is silent in production
 */

const { log, warn, error, debug, info } = require('./productionLogger');

/**
 * Log transaction data
 * 
 * @param {Object} data - Data to log
 * @param {string} type - Log type (info, error, warning, debug)
 * @param {string} action - Action being performed
 * @returns {Object} - Log entry
 */
const logTransaction = (data, type = 'info', action = 'transaction') => {
  const timestamp = new Date().toISOString();
  
  const logEntry = {
    timestamp,
    type,
    action,
    data: sanitizeLogData(data),
    environment: process.env.NODE_ENV || 'development'
  };
  
  // Console logging for Netlify Functions - production-safe
  const logMessage = `[${timestamp}] [${type.toUpperCase()}] [${action}]`;

  switch (type.toLowerCase()) {
    case 'error':
      error(logMessage, logEntry.data);
      break;
    case 'warning':
    case 'warn':
      warn(logMessage, logEntry.data);
      break;
    case 'debug':
      debug(logMessage, logEntry.data);
      break;
    default:
      log(logMessage, logEntry.data);
  }
  
  return logEntry;
};

/**
 * Log payment initiation
 * 
 * @param {Object} paymentData - Payment data
 * @returns {Object} - Log entry
 */
const logPaymentInitiation = (paymentData) => {
  const logData = {
    txnid: paymentData.txnid,
    amount: paymentData.amount,
    email: maskEmail(paymentData.email),
    firstname: paymentData.firstname,
    productinfo: paymentData.productinfo,
    timestamp: new Date().toISOString()
  };
  
  return logTransaction(logData, 'info', 'payment_initiated');
};

/**
 * Log payment response
 * 
 * @param {Object} responseData - PayU response data
 * @param {boolean} hashVerified - Whether hash was verified
 * @returns {Object} - Log entry
 */
const logPaymentResponse = (responseData, hashVerified = false) => {
  const logData = {
    txnid: responseData.txnid,
    mihpayid: responseData.mihpayid,
    status: responseData.status,
    amount: responseData.amount,
    mode: responseData.mode,
    email: maskEmail(responseData.email),
    firstname: responseData.firstname,
    hash_verified: hashVerified,
    error: responseData.error,
    error_message: responseData.error_Message,
    bank_ref_num: responseData.bank_ref_num,
    timestamp: new Date().toISOString()
  };
  
  const logType = responseData.status === 'success' ? 'info' : 'warning';
  return logTransaction(logData, logType, 'payment_response');
};

/**
 * Log payment verification
 * 
 * @param {string} txnid - Transaction ID
 * @param {Object} verificationResult - Verification result
 * @returns {Object} - Log entry
 */
const logPaymentVerification = (txnid, verificationResult) => {
  const logData = {
    txnid,
    verification_status: verificationResult.status,
    verification_result: verificationResult,
    timestamp: new Date().toISOString()
  };
  
  return logTransaction(logData, 'info', 'payment_verification');
};

/**
 * Log error
 * 
 * @param {Error|string} error - Error object or message
 * @param {Object} context - Additional context
 * @returns {Object} - Log entry
 */
const logError = (error, context = {}) => {
  const logData = {
    error_message: error.message || error,
    error_stack: error.stack,
    context: sanitizeLogData(context),
    timestamp: new Date().toISOString()
  };
  
  return logTransaction(logData, 'error', 'error_occurred');
};

/**
 * Log hash generation/verification
 * 
 * @param {Object} hashData - Hash-related data
 * @param {string} operation - Operation type (generation, verification)
 * @returns {Object} - Log entry
 */
const logHashOperation = (hashData, operation = 'generation') => {
  const logData = {
    operation,
    txnid: hashData.txnid,
    hash_preview: hashData.hash ? hashData.hash.substring(0, 10) + '...' : 'N/A',
    hash_verified: hashData.verified,
    timestamp: new Date().toISOString()
  };
  
  return logTransaction(logData, 'debug', `hash_${operation}`);
};

/**
 * Sanitize log data to remove sensitive information
 * 
 * @param {Object} data - Data to sanitize
 * @returns {Object} - Sanitized data
 */
const sanitizeLogData = (data) => {
  if (!data || typeof data !== 'object') {
    return data;
  }
  
  const sanitized = { ...data };
  
  // Fields to mask or remove
  const sensitiveFields = ['salt', 'hash', 'password', 'secret', 'key'];
  const maskFields = ['email', 'phone', 'card', 'cardnum'];
  
  Object.keys(sanitized).forEach(key => {
    const lowerKey = key.toLowerCase();
    
    // Remove sensitive fields completely
    if (sensitiveFields.some(field => lowerKey.includes(field))) {
      if (lowerKey.includes('hash') && sanitized[key]) {
        // Show only preview of hash
        sanitized[key] = sanitized[key].substring(0, 10) + '...';
      } else {
        sanitized[key] = '[REDACTED]';
      }
    }
    
    // Mask fields partially
    if (maskFields.some(field => lowerKey.includes(field))) {
      if (lowerKey.includes('email')) {
        sanitized[key] = maskEmail(sanitized[key]);
      } else if (lowerKey.includes('phone')) {
        sanitized[key] = maskPhone(sanitized[key]);
      } else if (lowerKey.includes('card')) {
        sanitized[key] = maskCard(sanitized[key]);
      }
    }
  });
  
  return sanitized;
};

/**
 * Mask email address
 * 
 * @param {string} email - Email to mask
 * @returns {string} - Masked email
 */
const maskEmail = (email) => {
  if (!email || typeof email !== 'string') return email;
  
  const [username, domain] = email.split('@');
  if (!username || !domain) return email;
  
  const maskedUsername = username.length > 2 
    ? username.substring(0, 2) + '*'.repeat(username.length - 2)
    : username;
  
  return `${maskedUsername}@${domain}`;
};

/**
 * Mask phone number
 * 
 * @param {string} phone - Phone to mask
 * @returns {string} - Masked phone
 */
const maskPhone = (phone) => {
  if (!phone || typeof phone !== 'string') return phone;
  
  const cleanPhone = phone.replace(/\D/g, '');
  if (cleanPhone.length < 4) return phone;
  
  return cleanPhone.substring(0, 2) + '*'.repeat(cleanPhone.length - 4) + cleanPhone.slice(-2);
};

/**
 * Mask card number
 * 
 * @param {string} card - Card number to mask
 * @returns {string} - Masked card number
 */
const maskCard = (card) => {
  if (!card || typeof card !== 'string') return card;
  
  const cleanCard = card.replace(/\D/g, '');
  if (cleanCard.length < 8) return card;
  
  return cleanCard.substring(0, 4) + '*'.repeat(cleanCard.length - 8) + cleanCard.slice(-4);
};

/**
 * Create structured log for debugging
 * 
 * @param {string} message - Log message
 * @param {Object} data - Additional data
 * @returns {Object} - Log entry
 */
const debugLog = (message, data = {}) => {
  // No debug logging in production
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return logTransaction({ message, ...data }, 'debug', 'debug');
};

module.exports = {
  logTransaction,
  logPaymentInitiation,
  logPaymentResponse,
  logPaymentVerification,
  logError,
  logHashOperation,
  sanitizeLogData,
  maskEmail,
  maskPhone,
  maskCard,
  debugLog
};
