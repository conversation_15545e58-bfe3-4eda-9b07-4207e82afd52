/**
 * PayU Payment Validator
 * Handles validation of payment data and responses
 */

import config from '../config';

export interface PayUValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface PayUFormData {
  name: string;
  email: string;
  mobile: string;
  address?: string;
  city?: string;
  pincode?: string;
  landArea?: string;
  soilType?: string;
  district?: string;
  taluk?: string;
}

class PayUValidator {
  /**
   * Validate form data before payment initiation
   */
  validateFormData(formData: PayUFormData): PayUValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required field validation
    if (!formData.name || formData.name.trim().length < 2) {
      errors.push('Name must be at least 2 characters long');
    }

    if (!formData.email || !this.isValidEmail(formData.email)) {
      errors.push('Valid email address is required');
    }

    if (!formData.mobile || !this.isValidMobile(formData.mobile)) {
      errors.push('Valid 10-digit mobile number is required');
    }

    // Optional field validation with warnings
    if (!formData.address || formData.address.trim().length < 5) {
      warnings.push('Address should be more detailed for better service');
    }

    if (!formData.city || formData.city.trim().length < 2) {
      warnings.push('City information is recommended');
    }

    if (!formData.pincode || !this.isValidPincode(formData.pincode)) {
      warnings.push('Valid pincode helps in better service delivery');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate Indian mobile number
   */
  private isValidMobile(mobile: string): boolean {
    // Remove any spaces, dashes, or other characters
    const cleanMobile = mobile.replace(/\D/g, '');
    
    // Check if it matches Indian mobile pattern
    return config.validation.allowedMobilePattern.test(cleanMobile);
  }

  /**
   * Validate Indian pincode
   */
  private isValidPincode(pincode: string): boolean {
    const pincodeRegex = /^[1-9][0-9]{5}$/;
    return pincodeRegex.test(pincode);
  }

  /**
   * Validate payment amount
   */
  validatePaymentAmount(amount: number): PayUValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (amount < config.validation.minPaymentAmount) {
      errors.push(`Payment amount must be at least ₹${config.validation.minPaymentAmount}`);
    }

    if (amount > config.validation.maxPaymentAmount) {
      errors.push(`Payment amount cannot exceed ₹${config.validation.maxPaymentAmount}`);
    }

    if (amount !== config.payu.paymentAmount) {
      errors.push('Payment amount does not match the registration fee');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate transaction ID format
   */
  validateTransactionId(txnid: string): boolean {
    // PayU transaction ID should be alphanumeric and between 3-50 characters
    const txnidRegex = /^[a-zA-Z0-9_-]{3,50}$/;
    return txnidRegex.test(txnid);
  }

  /**
   * Validate PayU payment response
   */
  validatePaymentResponse(response: any): PayUValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields validation
    if (!response.txnid) {
      errors.push('Transaction ID is missing');
    } else if (!this.validateTransactionId(response.txnid)) {
      errors.push('Invalid transaction ID format');
    }

    if (!response.status) {
      errors.push('Payment status is missing');
    }

    if (!response.amount) {
      errors.push('Payment amount is missing');
    } else {
      const amountValidation = this.validatePaymentAmount(parseFloat(response.amount));
      if (!amountValidation.isValid) {
        errors.push(...amountValidation.errors);
      }
    }

    if (!response.firstname) {
      errors.push('Customer name is missing');
    }

    if (!response.email) {
      errors.push('Customer email is missing');
    } else if (!this.isValidEmail(response.email)) {
      warnings.push('Customer email format may be invalid');
    }

    if (!response.phone) {
      errors.push('Customer phone is missing');
    } else if (!this.isValidMobile(response.phone)) {
      warnings.push('Customer phone format may be invalid');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate PayU hash (basic format check)
   */
  validateHash(hash: string): boolean {
    // PayU hash is typically a SHA-512 hash (128 characters)
    const hashRegex = /^[a-f0-9]{128}$/i;
    return hashRegex.test(hash);
  }

  /**
   * Sanitize form data for payment processing
   */
  sanitizeFormData(formData: PayUFormData): PayUFormData {
    return {
      name: this.sanitizeString(formData.name),
      email: this.sanitizeEmail(formData.email),
      mobile: this.sanitizeMobile(formData.mobile),
      address: formData.address ? this.sanitizeString(formData.address) : undefined,
      city: formData.city ? this.sanitizeString(formData.city) : undefined,
      pincode: formData.pincode ? this.sanitizeString(formData.pincode) : undefined,
      landArea: formData.landArea ? this.sanitizeString(formData.landArea) : undefined,
      soilType: formData.soilType ? this.sanitizeString(formData.soilType) : undefined,
      district: formData.district ? this.sanitizeString(formData.district) : undefined,
      taluk: formData.taluk ? this.sanitizeString(formData.taluk) : undefined
    };
  }

  /**
   * Sanitize string input
   */
  private sanitizeString(input: string): string {
    return input.trim().replace(/[<>"']/g, '');
  }

  /**
   * Sanitize email input
   */
  private sanitizeEmail(email: string): string {
    return email.trim().toLowerCase();
  }

  /**
   * Sanitize mobile number
   */
  private sanitizeMobile(mobile: string): string {
    // Remove all non-digit characters and ensure it starts with country code or not
    const cleaned = mobile.replace(/\D/g, '');
    
    // If it starts with 91 and is 12 digits, remove the country code
    if (cleaned.length === 12 && cleaned.startsWith('91')) {
      return cleaned.substring(2);
    }
    
    return cleaned;
  }

  /**
   * Get validation error messages in different languages
   */
  getLocalizedErrorMessage(error: string, language: string = 'en'): string {
    const errorMessages: { [key: string]: { [lang: string]: string } } = {
      'Name must be at least 2 characters long': {
        en: 'Name must be at least 2 characters long',
        hi: 'नाम कम से कम 2 अक्षर का होना चाहिए',
        kn: 'ಹೆಸರು ಕನಿಷ್ಠ 2 ಅಕ್ಷರಗಳಷ್ಟು ಇರಬೇಕು'
      },
      'Valid email address is required': {
        en: 'Valid email address is required',
        hi: 'वैध ईमेल पता आवश्यक है',
        kn: 'ಮಾನ್ಯವಾದ ಇಮೇಲ್ ವಿಳಾಸ ಅಗತ್ಯವಿದೆ'
      },
      'Valid 10-digit mobile number is required': {
        en: 'Valid 10-digit mobile number is required',
        hi: 'वैध 10-अंकीय मोबाइल नंबर आवश्यक है',
        kn: 'ಮಾನ್ಯವಾದ 10-ಅಂಕಿಯ ಮೊಬೈಲ್ ಸಂಖ್ಯೆ ಅಗತ್ಯವಿದೆ'
      }
    };

    return errorMessages[error]?.[language] || error;
  }
}

// Export singleton instance
const payuValidator = new PayUValidator();
export default payuValidator;
