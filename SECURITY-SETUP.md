# Security Setup Guide

This document explains how to set up sensitive configuration files that are not tracked by Git for security reasons.

## Environment Files

### 1. Production Environment Variables (`.env.production`)

This file contains production credentials and should never be committed to Git.

**Setup:**
1. Copy `.env.production.example` to `.env.production`
2. Update the following values with your actual credentials:
   - `PAYU_MERCHANT_KEY` - Your PayU production merchant key
   - `PAYU_MERCHANT_ID` - Your PayU production merchant ID
   - `PAYU_SALT_256` - Your PayU production salt
   - `REACT_APP_GOOGLE_SCRIPT_URL` - Your Google Apps Script URL
   - `REACT_APP_GEMINI_API_KEY` - Your Google Gemini API key for the chatbot
   - `REACT_APP_N8N_WEBHOOK_URL` - Your n8n webhook URL for email service

**For Netlify Deployment:**
Set these environment variables in your Netlify dashboard instead of using the `.env.production` file:
- Go to Site Settings > Environment Variables
- Add all the variables from `.env.production.example`
- Use your actual production values
- **Important**: Add `REACT_APP_GEMINI_API_KEY` to enable the AI chatbot feature
- **Important**: Add `CLOUDINARY_API_KEY` to enable media uploads in the CMS

### 2. PayU Configuration (`netlify/functions/utils/payuConfig.js`)

This file contains PayU API credentials and should not be committed to Git.

**Setup:**
1. Copy `netlify/functions/utils/payuConfig.example.js` to `netlify/functions/utils/payuConfig.js`
2. Replace the following placeholder values:
   - `YOUR_TEST_KEY` - Your PayU test merchant key
   - `YOUR_TEST_MERCHANT_ID` - Your PayU test merchant ID
   - `YOUR_TEST_SALT` - Your PayU test salt
   - `YOUR_CLIENT_ID` - Your PayU client ID
   - `YOUR_CLIENT_SECRET` - Your PayU client secret
   - `https://your-domain.com` - Your actual domain

## Security Best Practices

1. **Never commit sensitive files**: The `.gitignore` file is configured to exclude:
   - `.env.production`
   - `netlify/functions/utils/payuConfig.js`
   - Any files containing credentials, secrets, or keys

2. **Use environment variables**: For production deployments, use environment variables instead of files:
   - Netlify: Site Settings > Environment Variables
   - Vercel: Project Settings > Environment Variables
   - Heroku: Config Vars

3. **Keep example files updated**: When adding new configuration options:
   - Update the `.example` files with placeholder values
   - Document the new variables in this README
   - Never include actual credentials in example files

## Files Excluded from Git

The following sensitive files are automatically excluded from Git tracking:

```
# Environment files
.env.production
.env.*
!.env.example
!.env.*.example

# Configuration files with credentials
**/payuConfig.js
**/config/**/secrets.*
**/config/**/credentials.*

# API keys and secrets
**/*secret*
**/*key*
**/*credential*
```

## Troubleshooting

### Missing Configuration Error
If you see errors about missing PayU credentials:
1. Ensure you've created `payuConfig.js` from the example file
2. Verify all placeholder values have been replaced with actual credentials
3. Check that environment variables are set correctly in your deployment platform

### Test vs Production Mode
- Set `PAYU_TEST_MODE=true` to use test credentials
- Set `PAYU_TEST_MODE=false` or omit it to use production credentials
- Test credentials are safe to use in development but should not be used in production

## Contact

If you need help setting up credentials or have security concerns, contact the development team.
