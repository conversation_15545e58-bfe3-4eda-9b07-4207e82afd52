// Image optimization utilities for CMS
export interface ImageDimensions {
  width: number;
  height: number;
}

export interface OptimizationOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number; // 0-1
  format?: 'jpeg' | 'png' | 'webp';
  maintainAspectRatio?: boolean;
}

export interface OptimizedImage {
  blob: Blob;
  url: string;
  dimensions: ImageDimensions;
  size: number;
  format: string;
}

/**
 * Get image dimensions from a file
 */
export const getImageDimensions = (file: File): Promise<ImageDimensions> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(file);
    
    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image'));
    };
    
    img.src = url;
  });
};

/**
 * Calculate new dimensions while maintaining aspect ratio
 */
export const calculateDimensions = (
  original: ImageDimensions,
  maxWidth?: number,
  maxHeight?: number
): ImageDimensions => {
  let { width, height } = original;
  
  if (maxWidth && width > maxWidth) {
    height = (height * maxWidth) / width;
    width = maxWidth;
  }
  
  if (maxHeight && height > maxHeight) {
    width = (width * maxHeight) / height;
    height = maxHeight;
  }
  
  return { width: Math.round(width), height: Math.round(height) };
};

/**
 * Optimize image file
 */
export const optimizeImage = async (
  file: File,
  options: OptimizationOptions = {}
): Promise<OptimizedImage> => {
  const {
    maxWidth = 1920,
    maxHeight = 1080,
    quality = 0.8,
    format = 'jpeg',
    maintainAspectRatio = true
  } = options;

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }

    img.onload = () => {
      try {
        const originalDimensions = {
          width: img.naturalWidth,
          height: img.naturalHeight
        };

        const newDimensions = maintainAspectRatio
          ? calculateDimensions(originalDimensions, maxWidth, maxHeight)
          : { width: maxWidth, height: maxHeight };

        canvas.width = newDimensions.width;
        canvas.height = newDimensions.height;

        // Apply image smoothing for better quality
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';

        // Draw the resized image
        ctx.drawImage(img, 0, 0, newDimensions.width, newDimensions.height);

        // Convert to blob
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Failed to create optimized image'));
              return;
            }

            const optimizedUrl = URL.createObjectURL(blob);
            
            resolve({
              blob,
              url: optimizedUrl,
              dimensions: newDimensions,
              size: blob.size,
              format
            });
          },
          `image/${format}`,
          quality
        );
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image for optimization'));
    };

    img.src = URL.createObjectURL(file);
  });
};

/**
 * Generate multiple image sizes (responsive images)
 */
export const generateResponsiveImages = async (
  file: File,
  sizes: { name: string; width: number; height?: number }[] = [
    { name: 'thumbnail', width: 300, height: 200 },
    { name: 'medium', width: 800, height: 600 },
    { name: 'large', width: 1200, height: 800 },
    { name: 'original', width: 1920, height: 1080 }
  ]
): Promise<{ [key: string]: OptimizedImage }> => {
  const results: { [key: string]: OptimizedImage } = {};
  
  for (const size of sizes) {
    try {
      const optimized = await optimizeImage(file, {
        maxWidth: size.width,
        maxHeight: size.height,
        quality: 0.8,
        format: 'jpeg'
      });
      results[size.name] = optimized;
    } catch (error) {
      console.error(`Failed to generate ${size.name} size:`, error);
    }
  }
  
  return results;
};

/**
 * Validate image file
 */
export const validateImageFile = (
  file: File,
  options: {
    maxSize?: number; // in bytes
    allowedFormats?: string[];
    minWidth?: number;
    minHeight?: number;
    maxWidth?: number;
    maxHeight?: number;
  } = {}
): Promise<{ valid: boolean; errors: string[] }> => {
  const {
    maxSize = 10 * 1024 * 1024, // 10MB
    allowedFormats = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    minWidth = 100,
    minHeight = 100,
    maxWidth = 5000,
    maxHeight = 5000
  } = options;

  return new Promise(async (resolve) => {
    const errors: string[] = [];

    // Check file size
    if (file.size > maxSize) {
      errors.push(`File size (${Math.round(file.size / 1024 / 1024)}MB) exceeds maximum allowed size (${Math.round(maxSize / 1024 / 1024)}MB)`);
    }

    // Check file format
    if (!allowedFormats.includes(file.type)) {
      errors.push(`File format ${file.type} is not allowed. Allowed formats: ${allowedFormats.join(', ')}`);
    }

    // Check image dimensions
    try {
      const dimensions = await getImageDimensions(file);
      
      if (dimensions.width < minWidth || dimensions.height < minHeight) {
        errors.push(`Image dimensions (${dimensions.width}x${dimensions.height}) are too small. Minimum: ${minWidth}x${minHeight}`);
      }
      
      if (dimensions.width > maxWidth || dimensions.height > maxHeight) {
        errors.push(`Image dimensions (${dimensions.width}x${dimensions.height}) are too large. Maximum: ${maxWidth}x${maxHeight}`);
      }
    } catch (error) {
      errors.push('Failed to read image dimensions');
    }

    resolve({
      valid: errors.length === 0,
      errors
    });
  });
};

/**
 * Convert image to WebP format for better compression
 */
export const convertToWebP = async (file: File, quality: number = 0.8): Promise<OptimizedImage> => {
  return optimizeImage(file, {
    format: 'webp',
    quality,
    maxWidth: 1920,
    maxHeight: 1080
  });
};

/**
 * Create image thumbnail
 */
export const createThumbnail = async (
  file: File,
  size: number = 150
): Promise<OptimizedImage> => {
  return optimizeImage(file, {
    maxWidth: size,
    maxHeight: size,
    quality: 0.7,
    format: 'jpeg'
  });
};

/**
 * Batch optimize multiple images
 */
export const batchOptimizeImages = async (
  files: File[],
  options: OptimizationOptions = {},
  onProgress?: (completed: number, total: number) => void
): Promise<OptimizedImage[]> => {
  const results: OptimizedImage[] = [];
  
  for (let i = 0; i < files.length; i++) {
    try {
      const optimized = await optimizeImage(files[i], options);
      results.push(optimized);
      
      if (onProgress) {
        onProgress(i + 1, files.length);
      }
    } catch (error) {
      console.error(`Failed to optimize image ${files[i].name}:`, error);
    }
  }
  
  return results;
};

/**
 * Get image metadata
 */
export const getImageMetadata = async (file: File) => {
  const dimensions = await getImageDimensions(file);
  
  return {
    name: file.name,
    size: file.size,
    type: file.type,
    lastModified: new Date(file.lastModified),
    dimensions,
    aspectRatio: dimensions.width / dimensions.height
  };
};

/**
 * Clean up blob URLs to prevent memory leaks
 */
export const cleanupBlobUrl = (url: string) => {
  if (url.startsWith('blob:')) {
    URL.revokeObjectURL(url);
  }
};
