/**
 * Farmland Contact Form Component
 * Allows users to send inquiries about farmland
 */

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Grid
} from '@mui/material';
import { Send as SendIcon, Email as EmailIcon } from '@mui/icons-material';
import farmlandEmailService from '../services/farmlandEmailService';

interface FarmlandContactFormProps {
  language: string;
}

const FarmlandContactForm: React.FC<FarmlandContactFormProps> = ({ language }) => {
  const [formData, setFormData] = useState({
    email: '',
    contactNumber: '',
    message: ''
  });
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');

  const translations = {
    en: {
      title: 'Interested in Our Farmlands?',
      subtitle: 'Send us your inquiry and we\'ll get back to you soon',
      emailLabel: 'Your Email',
      contactNumberLabel: 'Contact Number',
      messageLabel: 'Your Message',
      sendButton: 'Send Message',
      successMessage: 'Message sent successfully! We will contact you soon.',
      errorMessage: 'Failed to send message. Please try again.',
      emailRequired: 'Email is required',
      contactNumberRequired: 'Contact number is required',
      messageRequired: 'Message is required',
      invalidEmail: 'Please enter a valid email address',
      invalidContactNumber: 'Please enter a valid 10-digit mobile number',
      contactNumber: 'Or call us directly at: +91 99868 90777'
    },
    hi: {
      title: 'हमारी कृषि भूमि में रुचि है?',
      subtitle: 'हमें अपनी पूछताछ भेजें और हम जल्द ही आपसे संपर्क करेंगे',
      emailLabel: 'आपका ईमेल',
      contactNumberLabel: 'संपर्क नंबर',
      messageLabel: 'आपका संदेश',
      sendButton: 'संदेश भेजें',
      successMessage: 'संदेश सफलतापूर्वक भेजा गया! हम जल्द ही आपसे संपर्क करेंगे।',
      errorMessage: 'संदेश भेजने में विफल। कृपया पुनः प्रयास करें।',
      emailRequired: 'ईमेल आवश्यक है',
      contactNumberRequired: 'संपर्क नंबर आवश्यक है',
      messageRequired: 'संदेश आवश्यक है',
      invalidEmail: 'कृपया एक वैध ईमेल पता दर्ज करें',
      invalidContactNumber: 'कृपया एक वैध 10-अंकीय मोबाइल नंबर दर्ज करें',
      contactNumber: 'या हमें सीधे कॉल करें: +91 99868 90777'
    },
    kn: {
      title: 'ನಮ್ಮ ಕೃಷಿ ಭೂಮಿಯಲ್ಲಿ ಆಸಕ್ತಿ ಇದೆಯೇ?',
      subtitle: 'ನಿಮ್ಮ ವಿಚಾರಣೆಯನ್ನು ನಮಗೆ ಕಳುಹಿಸಿ ಮತ್ತು ನಾವು ಶೀಘ್ರದಲ್ಲೇ ನಿಮ್ಮನ್ನು ಸಂಪರ್ಕಿಸುತ್ತೇವೆ',
      emailLabel: 'ನಿಮ್ಮ ಇಮೇಲ್',
      contactNumberLabel: 'ಸಂಪರ್ಕ ಸಂಖ್ಯೆ',
      messageLabel: 'ನಿಮ್ಮ ಸಂದೇಶ',
      sendButton: 'ಸಂದೇಶ ಕಳುಹಿಸಿ',
      successMessage: 'ಸಂದೇಶವನ್ನು ಯಶಸ್ವಿಯಾಗಿ ಕಳುಹಿಸಲಾಗಿದೆ! ನಾವು ಶೀಘ್ರದಲ್ಲೇ ನಿಮ್ಮನ್ನು ಸಂಪರ್ಕಿಸುತ್ತೇವೆ।',
      errorMessage: 'ಸಂದೇಶ ಕಳುಹಿಸಲು ವಿಫಲವಾಗಿದೆ। ದಯವಿಟ್ಟು ಮತ್ತೆ ಪ್ರಯತ್ನಿಸಿ।',
      emailRequired: 'ಇಮೇಲ್ ಅಗತ್ಯವಿದೆ',
      contactNumberRequired: 'ಸಂಪರ್ಕ ಸಂಖ್ಯೆ ಅಗತ್ಯವಿದೆ',
      messageRequired: 'ಸಂದೇಶ ಅಗತ್ಯವಿದೆ',
      invalidEmail: 'ದಯವಿಟ್ಟು ಮಾನ್ಯವಾದ ಇಮೇಲ್ ವಿಳಾಸವನ್ನು ನಮೂದಿಸಿ',
      invalidContactNumber: 'ದಯವಿಟ್ಟು ಮಾನ್ಯವಾದ 10-ಅಂಕಿಯ ಮೊಬೈಲ್ ಸಂಖ್ಯೆಯನ್ನು ನಮೂದಿಸಿ',
      contactNumber: 'ಅಥವಾ ನಮಗೆ ನೇರವಾಗಿ ಕರೆ ಮಾಡಿ: +91 99868 90777'
    }
  };

  const t = translations[language as keyof typeof translations] || translations.en;

  const validateForm = () => {
    if (!formData.email.trim()) {
      setError(t.emailRequired);
      return false;
    }
    if (!formData.contactNumber.trim()) {
      setError(t.contactNumberRequired);
      return false;
    }
    if (!formData.message.trim()) {
      setError(t.messageRequired);
      return false;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError(t.invalidEmail);
      return false;
    }
    const phoneRegex = /^[6-9]\d{9}$/;
    if (!phoneRegex.test(formData.contactNumber)) {
      setError(t.invalidContactNumber);
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess(false);

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Use the new farmland email service
      const result = await farmlandEmailService.sendFarmlandInquiry({
        email: formData.email,
        contactNumber: formData.contactNumber,
        message: formData.message,
        timestamp: new Date().toISOString()
      });

      if (result.success) {
        setSuccess(true);
        setFormData({ email: '', contactNumber: '', message: '' });
      } else {
        setError(result.message || t.errorMessage);
      }
    } catch (error) {
      setError(t.errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError('');
  };

  return (
    <Card
      elevation={3}
      sx={{
        maxWidth: 600,
        mx: 'auto',
        borderRadius: '16px',
        overflow: 'hidden'
      }}
    >
      <CardContent sx={{ p: { xs: 3, sm: 4 } }}>
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <EmailIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
          <Typography variant="h5" gutterBottom fontWeight={600} color="primary">
            {t.title}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {t.subtitle}
          </Typography>
        </Box>

        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {t.successMessage}
          </Alert>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Box component="form" onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t.emailLabel}
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                required
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '12px'
                  }
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t.contactNumberLabel}
                type="tel"
                value={formData.contactNumber}
                onChange={(e) => handleInputChange('contactNumber', e.target.value)}
                required
                variant="outlined"
                inputProps={{
                  inputMode: 'numeric',
                  pattern: '[0-9]*'
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '12px'
                  }
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t.messageLabel}
                multiline
                rows={4}
                value={formData.message}
                onChange={(e) => handleInputChange('message', e.target.value)}
                required
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '12px'
                  }
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                sx={{
                  borderRadius: '12px',
                  py: 1.5,
                  fontSize: '1rem',
                  fontWeight: 600,
                  background: 'linear-gradient(45deg, #1B4C35 30%, #2E7D32 90%)',
                  '&:hover': {
                    background: 'linear-gradient(45deg, #0A3622 30%, #1B4C35 90%)',
                  }
                }}
              >
                {loading ? 'Sending...' : t.sendButton}
              </Button>
            </Grid>
          </Grid>
        </Box>

        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            {t.contactNumber}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default FarmlandContactForm;
