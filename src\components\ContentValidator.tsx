import React from 'react';
import {
  Box,
  Typography,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Card,
  CardContent,
  LinearProgress
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon
} from '@mui/icons-material';

interface ValidationRule {
  id: string;
  field: string;
  type: 'required' | 'format' | 'length' | 'image' | 'custom';
  message: string;
  severity: 'error' | 'warning' | 'info';
  validator?: (value: any) => boolean;
}

interface ValidationResult {
  field: string;
  rule: ValidationRule;
  passed: boolean;
  message: string;
}

interface ContentValidatorProps {
  content: any;
  rules: ValidationRule[];
  onValidationChange?: (isValid: boolean, results: ValidationResult[]) => void;
}

const ContentValidator: React.FC<ContentValidatorProps> = ({
  content,
  rules,
  onValidationChange
}) => {
  // Validate content against rules
  const validateContent = (): ValidationResult[] => {
    const results: ValidationResult[] = [];

    rules.forEach(rule => {
      const fieldValue = getNestedValue(content, rule.field);
      let passed = true;
      let message = rule.message;

      switch (rule.type) {
        case 'required':
          passed = fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
          if (Array.isArray(fieldValue)) {
            passed = fieldValue.length > 0;
          }
          break;

        case 'format':
          if (rule.validator) {
            passed = rule.validator(fieldValue);
          }
          break;

        case 'length':
          if (typeof fieldValue === 'string') {
            const minLength = rule.validator ? 10 : 0; // Default min length
            passed = fieldValue.length >= minLength;
            message = `${rule.field} must be at least ${minLength} characters long`;
          }
          break;

        case 'image':
          if (Array.isArray(fieldValue)) {
            passed = fieldValue.length > 0 && fieldValue.every(img => 
              img.url && img.alt && img.title
            );
            message = passed ? 'Images are properly configured' : 'Images missing required fields (url, alt, title)';
          } else {
            passed = fieldValue && fieldValue.url && fieldValue.alt;
            message = passed ? 'Image is properly configured' : 'Image missing required fields';
          }
          break;

        case 'custom':
          if (rule.validator) {
            passed = rule.validator(fieldValue);
          }
          break;
      }

      results.push({
        field: rule.field,
        rule,
        passed,
        message
      });
    });

    return results;
  };

  // Get nested object value by path
  const getNestedValue = (obj: any, path: string): any => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  };

  const validationResults = validateContent();
  const errorCount = validationResults.filter(r => !r.passed && r.rule.severity === 'error').length;
  const warningCount = validationResults.filter(r => !r.passed && r.rule.severity === 'warning').length;
  const totalRules = validationResults.length;
  const passedRules = validationResults.filter(r => r.passed).length;
  const validationScore = Math.round((passedRules / totalRules) * 100);

  // Notify parent component of validation status
  React.useEffect(() => {
    if (onValidationChange) {
      const isValid = errorCount === 0;
      onValidationChange(isValid, validationResults);
    }
  }, [errorCount, validationResults, onValidationChange]);

  const getIcon = (result: ValidationResult) => {
    if (result.passed) {
      return <CheckIcon color="success" />;
    }
    
    switch (result.rule.severity) {
      case 'error':
        return <ErrorIcon color="error" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'info':
        return <InfoIcon color="info" />;
      default:
        return <ErrorIcon color="error" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'info':
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ mb: 2 }}>
          <Typography variant="h6" gutterBottom>
            Content Validation
          </Typography>
          
          {/* Validation Score */}
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2">
                Validation Score: {validationScore}%
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                {errorCount > 0 && (
                  <Chip 
                    label={`${errorCount} Errors`} 
                    color="error" 
                    size="small" 
                  />
                )}
                {warningCount > 0 && (
                  <Chip 
                    label={`${warningCount} Warnings`} 
                    color="warning" 
                    size="small" 
                  />
                )}
                {errorCount === 0 && warningCount === 0 && (
                  <Chip 
                    label="All Good!" 
                    color="success" 
                    size="small" 
                  />
                )}
              </Box>
            </Box>
            <LinearProgress 
              variant="determinate" 
              value={validationScore} 
              color={validationScore === 100 ? 'success' : validationScore >= 70 ? 'primary' : 'error'}
            />
          </Box>

          {/* Overall Status */}
          {errorCount === 0 && warningCount === 0 && (
            <Alert severity="success" sx={{ mb: 2 }}>
              All validation rules passed! Your content is ready for publication.
            </Alert>
          )}
          
          {errorCount > 0 && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {errorCount} critical issue{errorCount > 1 ? 's' : ''} found. Please fix before publishing.
            </Alert>
          )}
          
          {warningCount > 0 && errorCount === 0 && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              {warningCount} warning{warningCount > 1 ? 's' : ''} found. Consider reviewing before publishing.
            </Alert>
          )}
        </Box>

        {/* Validation Results */}
        <List dense>
          {validationResults.map((result, index) => (
            <ListItem key={index}>
              <ListItemIcon>
                {getIcon(result)}
              </ListItemIcon>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2">
                      {result.field}
                    </Typography>
                    <Chip 
                      label={result.rule.severity} 
                      size="small" 
                      color={getSeverityColor(result.rule.severity) as any}
                      variant="outlined"
                    />
                  </Box>
                }
                secondary={result.message}
              />
            </ListItem>
          ))}
        </List>

        {/* Quick Fix Suggestions */}
        {(errorCount > 0 || warningCount > 0) && (
          <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
            <Typography variant="subtitle2" gutterBottom>
              Quick Fix Suggestions:
            </Typography>
            <List dense>
              {validationResults
                .filter(r => !r.passed)
                .slice(0, 3) // Show top 3 issues
                .map((result, index) => (
                  <ListItem key={index} sx={{ pl: 0 }}>
                    <ListItemText
                      primary={`Fix ${result.field}`}
                      secondary={getQuickFixSuggestion(result)}
                    />
                  </ListItem>
                ))}
            </List>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

// Helper function to provide quick fix suggestions
const getQuickFixSuggestion = (result: ValidationResult): string => {
  switch (result.rule.type) {
    case 'required':
      return `Add content to the ${result.field} field`;
    case 'image':
      return `Upload an image and add alt text and title`;
    case 'length':
      return `Add more descriptive content to meet minimum length requirements`;
    case 'format':
      return `Check the format of ${result.field} and ensure it meets requirements`;
    default:
      return `Review and fix the ${result.field} field`;
  }
};

export default ContentValidator;
