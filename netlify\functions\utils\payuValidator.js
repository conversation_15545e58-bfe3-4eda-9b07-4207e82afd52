/**
 * PayU Validator Utility
 * Handles validation and sanitization of payment data
 * Converted from PHP validation logic to JavaScript
 */

/**
 * Validate payment request data
 * 
 * @param {Object} requestData - Payment request data
 * @returns {Object} - Validation result
 */
const validatePaymentRequest = (requestData) => {
  const errors = [];
  
  // Required fields validation
  if (!requestData.firstname || requestData.firstname.trim() === '') {
    errors.push('First name is required');
  }
  
  if (!requestData.email || requestData.email.trim() === '') {
    errors.push('Email address is required');
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(requestData.email.trim())) {
    errors.push('Valid email address is required');
  }
  
  if (!requestData.phone || requestData.phone.trim() === '') {
    errors.push('Phone number is required');
  } else if (!/^[0-9]{10}$/.test(requestData.phone.trim().replace(/\D/g, ''))) {
    errors.push('Valid 10-digit phone number is required');
  }
  
  if (!requestData.txnid || requestData.txnid.trim() === '') {
    errors.push('Transaction ID is required');
  }
  
  if (!requestData.amount) {
    errors.push('Amount is required');
  } else {
    const amount = parseFloat(requestData.amount);
    if (isNaN(amount) || amount <= 0) {
      errors.push('Valid amount is required');
    }
  }
  
  // Optional field validation
  if (requestData.productinfo && requestData.productinfo.length > 100) {
    errors.push('Product info must be less than 100 characters');
  }
  
  if (requestData.firstname && requestData.firstname.length > 60) {
    errors.push('First name must be less than 60 characters');
  }
  
  if (requestData.lastname && requestData.lastname.length > 60) {
    errors.push('Last name must be less than 60 characters');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitize payment parameters
 * 
 * @param {Object} requestData - Raw request data
 * @returns {Object} - Sanitized data
 */
const sanitizePaymentParams = (requestData) => {
  const sanitized = {};
  
  // Helper function to sanitize string
  const sanitizeString = (str) => {
    if (!str) return '';
    return str.toString().trim().replace(/[<>\"'&]/g, '');
  };
  
  // Helper function to sanitize phone number
  const sanitizePhone = (phone) => {
    if (!phone) return '';
    return phone.toString().replace(/\D/g, '').slice(0, 10);
  };
  
  // Helper function to sanitize amount
  const sanitizeAmount = (amount) => {
    if (!amount) return '0.00';
    const num = parseFloat(amount);
    return isNaN(num) ? '0.00' : num.toFixed(2);
  };
  
  // Sanitize required fields
  sanitized.firstname = sanitizeString(requestData.firstname);
  sanitized.lastname = sanitizeString(requestData.lastname);
  sanitized.email = sanitizeString(requestData.email).toLowerCase();
  sanitized.phone = sanitizePhone(requestData.phone);
  sanitized.txnid = sanitizeString(requestData.txnid);
  sanitized.amount = sanitizeAmount(requestData.amount);
  sanitized.productinfo = sanitizeString(requestData.productinfo) || 'Darvi Group Payment';
  
  // Sanitize optional fields
  sanitized.address1 = sanitizeString(requestData.address1 || requestData.address);
  sanitized.city = sanitizeString(requestData.city);
  sanitized.state = sanitizeString(requestData.state);
  sanitized.country = sanitizeString(requestData.country) || 'India';
  sanitized.zipcode = sanitizeString(requestData.zipcode);
  
  // Sanitize UDF fields (User Defined Fields)
  sanitized.udf1 = sanitizeString(requestData.udf1);
  sanitized.udf2 = sanitizeString(requestData.udf2);
  sanitized.udf3 = sanitizeString(requestData.udf3);
  sanitized.udf4 = sanitizeString(requestData.udf4);
  sanitized.udf5 = sanitizeString(requestData.udf5);
  
  return sanitized;
};

/**
 * Generate unique transaction ID
 * 
 * @param {string} prefix - Optional prefix for transaction ID
 * @returns {string} - Unique transaction ID
 */
const generateTransactionId = (prefix = 'TXN') => {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 9000 + 1000);
  return `${prefix}${timestamp}${random}`;
};

/**
 * Validate PayU response data
 * 
 * @param {Object} responseData - Response data from PayU
 * @returns {Object} - Validation result
 */
const validatePayUResponse = (responseData) => {
  const errors = [];
  
  // Required fields in PayU response
  const requiredFields = ['key', 'txnid', 'amount', 'productinfo', 'firstname', 'email', 'status', 'hash'];
  
  requiredFields.forEach(field => {
    if (!responseData[field] || responseData[field].toString().trim() === '') {
      errors.push(`${field} is missing in PayU response`);
    }
  });
  
  // Validate status
  const validStatuses = ['success', 'failure', 'pending', 'cancel'];
  if (responseData.status && !validStatuses.includes(responseData.status.toLowerCase())) {
    errors.push('Invalid payment status in response');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitize PayU response data
 * 
 * @param {Object} responseData - Raw response data from PayU
 * @returns {Object} - Sanitized response data
 */
const sanitizePayUResponse = (responseData) => {
  const sanitized = {};
  
  // Helper function to sanitize string
  const sanitizeString = (str) => {
    if (!str) return '';
    return str.toString().trim();
  };
  
  // Sanitize all response fields
  Object.keys(responseData).forEach(key => {
    sanitized[key] = sanitizeString(responseData[key]);
  });
  
  return sanitized;
};

/**
 * Validate amount format
 * 
 * @param {string|number} amount - Amount to validate
 * @returns {Object} - Validation result
 */
const validateAmount = (amount) => {
  const num = parseFloat(amount);
  
  if (isNaN(num)) {
    return {
      isValid: false,
      error: 'Amount must be a valid number'
    };
  }
  
  if (num <= 0) {
    return {
      isValid: false,
      error: 'Amount must be greater than 0'
    };
  }
  
  if (num > 100000) {
    return {
      isValid: false,
      error: 'Amount cannot exceed ₹1,00,000'
    };
  }
  
  return {
    isValid: true,
    amount: num.toFixed(2)
  };
};

/**
 * Validate email format
 * 
 * @param {string} email - Email to validate
 * @returns {Object} - Validation result
 */
const validateEmail = (email) => {
  if (!email || email.trim() === '') {
    return {
      isValid: false,
      error: 'Email is required'
    };
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email.trim())) {
    return {
      isValid: false,
      error: 'Invalid email format'
    };
  }
  
  return {
    isValid: true,
    email: email.trim().toLowerCase()
  };
};

/**
 * Validate phone number
 * 
 * @param {string} phone - Phone number to validate
 * @returns {Object} - Validation result
 */
const validatePhone = (phone) => {
  if (!phone || phone.trim() === '') {
    return {
      isValid: false,
      error: 'Phone number is required'
    };
  }
  
  const cleanPhone = phone.toString().replace(/\D/g, '');
  
  if (cleanPhone.length !== 10) {
    return {
      isValid: false,
      error: 'Phone number must be 10 digits'
    };
  }
  
  return {
    isValid: true,
    phone: cleanPhone
  };
};

module.exports = {
  validatePaymentRequest,
  sanitizePaymentParams,
  generateTransactionId,
  validatePayUResponse,
  sanitizePayUResponse,
  validateAmount,
  validateEmail,
  validatePhone
};
