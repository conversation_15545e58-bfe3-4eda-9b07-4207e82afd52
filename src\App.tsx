import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme, responsiveFontSizes } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import Navbar from './components/Navbar';
import Home from './pages/Home';
import Contact from './pages/Contact';
import About from './pages/About';
import FAQ from './pages/FAQ';
import Form from './pages/Form';
import Services from './pages/Services';
import Pricing from './pages/Pricing';
import Checkout from './pages/Checkout';
import Terms from './pages/Terms';
import IoTManagement from './pages/IoTManagement';
import RegistrationSuccess from './pages/RegistrationSuccess';
import PaymentSuccess from './pages/PaymentSuccess';
import PaymentFailure from './pages/PaymentFailure';
import ResearchCenter from './pages/ResearchCenter';
import Blog from './pages/Blog';
import CMSAdmin from './components/CMSAdmin';
import Layout from './components/Layout';
import ChatBot from './components/ChatBot';
import { LanguageProvider } from './contexts/LanguageContext';
import { CMSProvider } from './contexts/CMSContext';
import { SecurityManager } from './utils/security';
import '@fontsource/poppins';
import '@fontsource/poppins/500.css';
import '@fontsource/poppins/600.css';
import '@fontsource/poppins/700.css';
import '@fontsource/playfair-display';
import '@fontsource/playfair-display/700.css';





let theme = createTheme({
  palette: {
    primary: {
      main: '#1B4C35', // Darker green from the image
      light: '#4CAF50',
      dark: '#0A3622',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#FF6B35', // Vibrant orange for accents
      light: '#FF8C61',
      dark: '#E54B00',
      contrastText: '#ffffff',
    },
    success: {
      main: '#4CAF50',
      light: '#81C784',
      dark: '#388E3C',
    },
    info: {
      main: '#2196F3',
      light: '#64B5F6',
      dark: '#0D47A1',
    },
    background: {
      default: '#F8F9FA',
      paper: '#FFFFFF',
    },
  },
  typography: {
    fontFamily: '"Poppins", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontFamily: '"Playfair Display", serif',
      fontWeight: 700,
      fontSize: '4.5rem',
      lineHeight: 1.1,
      letterSpacing: '-0.02em',
    },
    h2: {
      fontFamily: '"Playfair Display", serif',
      fontWeight: 700,
      fontSize: '3.5rem',
      lineHeight: 1.2,
      letterSpacing: '-0.01em',
    },
    h3: {
      fontFamily: '"Playfair Display", serif',
      fontWeight: 600,
      fontSize: '2.75rem',
      lineHeight: 1.3,
    },
    h4: {
      fontWeight: 600,
      fontSize: '2rem',
      lineHeight: 1.4,
    },
    h5: {
      fontWeight: 600,
      fontSize: '1.5rem',
      lineHeight: 1.4,
    },
    h6: {
      fontWeight: 500,
      fontSize: '1.25rem',
      lineHeight: 1.4,
    },
    subtitle1: {
      fontSize: '1.125rem',
      fontWeight: 500,
      lineHeight: 1.6,
    },
    subtitle2: {
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.6,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.7,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.7,
    },
    button: {
      textTransform: 'none',
      fontWeight: 600,
      letterSpacing: '0.5px',
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: '50px',
          padding: '12px 32px',
          fontSize: '1rem',
          minHeight: '44px', // Ensure minimum touch target
          transition: 'all 0.3s ease',
          position: 'relative',
          overflow: 'hidden',
          '@media (max-width: 600px)': {
            minHeight: '48px', // Larger touch target on mobile
            fontSize: '0.875rem',
            padding: '14px 24px',
          },
          '&::after': {
            content: '""',
            position: 'absolute',
            width: '100%',
            height: '100%',
            top: 0,
            left: '-100%',
            background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
            transition: 'all 0.5s ease',
          },
          '&:hover::after': {
            left: '100%',
          },
        },
        contained: {
          boxShadow: '0 4px 14px 0 rgba(0,0,0,0.1)',
          '&:hover': {
            boxShadow: '0 6px 20px rgba(0,0,0,0.2)',
            transform: 'translateY(-3px)',
          },
        },
        outlined: {
          borderWidth: '2px',
          '&:hover': {
            borderWidth: '2px',
            transform: 'translateY(-3px)',
            boxShadow: '0 6px 20px rgba(0,0,0,0.1)',
          },
        },
        containedPrimary: {
          background: 'linear-gradient(45deg, #1B4C35 30%, #2E7D32 90%)',
          '&:hover': {
            background: 'linear-gradient(45deg, #0A3622 30%, #1B4C35 90%)',
          },
        },
        containedSecondary: {
          background: 'linear-gradient(45deg, #E54B00 30%, #FF6B35 90%)',
          '&:hover': {
            background: 'linear-gradient(45deg, #D44000 30%, #E54B00 90%)',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: '16px',
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.08)',
          overflow: 'hidden',
          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
          '&:hover': {
            transform: 'translateY(-8px)',
            boxShadow: '0 16px 40px rgba(0, 0, 0, 0.12)',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: '16px',
          transition: 'box-shadow 0.3s ease',
        },
        elevation1: {
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
        },
        elevation2: {
          boxShadow: '0 6px 24px rgba(0, 0, 0, 0.06)',
        },
        elevation3: {
          boxShadow: '0 8px 28px rgba(0, 0, 0, 0.07)',
        },
        elevation4: {
          boxShadow: '0 10px 32px rgba(0, 0, 0, 0.08)',
        },
      },
    },
    MuiContainer: {
      styleOverrides: {
        root: {
          paddingLeft: '16px',
          paddingRight: '16px',
          '@media (min-width: 600px)': {
            paddingLeft: '24px',
            paddingRight: '24px',
          },
          '@media (min-width: 900px)': {
            paddingLeft: '32px',
            paddingRight: '32px',
          },
        },
      },
    },
    MuiTypography: {
      styleOverrides: {
        root: {
          transition: 'color 0.3s ease',
        },
        body1: {
          fontSize: '1rem',
          lineHeight: 1.5,
          '@media (max-width: 600px)': {
            fontSize: '1rem', // Ensure minimum 16px on mobile
            lineHeight: 1.4,
          },
        },
        body2: {
          fontSize: '0.875rem',
          lineHeight: 1.4,
          '@media (max-width: 600px)': {
            fontSize: '0.875rem', // 14px minimum for secondary text
            lineHeight: 1.4,
          },
        },
        caption: {
          fontSize: '0.75rem',
          lineHeight: 1.3,
          '@media (max-width: 600px)': {
            fontSize: '0.75rem', // 12px for captions
            lineHeight: 1.3,
          },
        },
      },
    },
    MuiLink: {
      styleOverrides: {
        root: {
          textDecoration: 'none',
          position: 'relative',
          '&::after': {
            content: '""',
            position: 'absolute',
            width: '0',
            height: '2px',
            bottom: '-2px',
            left: '0',
            backgroundColor: 'currentColor',
            transition: 'width 0.3s ease',
          },
          '&:hover::after': {
            width: '100%',
          },
        },
      },
    },
  },
});

// Make typography responsive
theme = responsiveFontSizes(theme);

function App() {
  // Initialize security on app startup
  useEffect(() => {
    // Initialize security manager
    SecurityManager.initialize();

    // App initialized successfully
  }, []);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <CMSProvider>
        <LanguageProvider>
          <Router>
            <Layout>
              <Navbar />
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/contact" element={<Contact />} />
                <Route path="/about" element={<About />} />
                <Route path="/services" element={<Services />} />
                <Route path="/pricing" element={<Pricing />} />
                <Route path="/checkout" element={<Checkout />} />
                <Route path="/iot-management" element={<IoTManagement />} />
                <Route path="/research-center" element={<ResearchCenter />} />
                <Route path="/blog" element={<Blog />} />
                <Route path="/faq" element={<FAQ />} />
                <Route path="/form" element={<Form />} />
                <Route path="/registration-success" element={<RegistrationSuccess />} />
                <Route path="/payment-success" element={<PaymentSuccess />} />
                <Route path="/payment-failure" element={<PaymentFailure />} />
                <Route path="/terms" element={<Terms />} />
                <Route path="/cms-admin" element={<CMSAdmin />} />
              </Routes>

              {/* Global ChatBot - appears on all pages */}
              <ChatBot />
            </Layout>
          </Router>
        </LanguageProvider>
      </CMSProvider>
    </ThemeProvider>
  );
}

export default App;
