import axios from 'axios';

// Define the context information about Darvi Group to guide the AI responses
const DARVI_CONTEXT = `
You are an AI assistant for Darvi Group, a company focused on sustainable agricultural solutions. Your name is Darvi Assistant.

About Darvi Group:
- Founded in 2018 and based in Hubli, Karnataka, India
- Provides comprehensive agricultural consultation and support services
- Offers a Farmer Registration Program (₹4,725 one-time lifetime membership)
- Specializes in sustainable farming practices, soil health, and crop optimization
- Provides IoT-powered agriculture monitoring solutions
- Serves both individual farmers and commercial agricultural enterprises across Karnataka

Current Business Model:
- Primary Service: Farmer Registration Program (₹4,725 lifetime membership)
- NOT selling individual products currently
- Focus on consultation, guidance, and agricultural support services

Farmer Registration Program Benefits (₹4,725 lifetime membership):
1. Free Agricultural Consultation - Personalized consultation with agricultural experts
2. Customized Crop Recommendations - Based on soil analysis and local climate conditions
3. Soil Health Assessment - Professional soil health evaluation and improvement recommendations
4. Expert Support & Guidance - 24/7 access to agricultural experts for ongoing support
5. Access to Premium Products - Exclusive access to agricultural products at member prices
6. IoT Solutions Trial - Free trial of IoT-based smart farming solutions for crop monitoring

Services Included:
- Agricultural consultation: Expert advice on crop selection, soil management, and farming techniques
- Sustainable farming practices: Eco-friendly methods that reduce environmental impact
- IoT-powered agriculture monitoring: Real-time tracking of soil moisture, temperature, and other metrics
- Land management advice: Optimizing land use for maximum productivity
- Government scheme assistance: Help with farmer registration for government programs and subsidies

Registration Process:
1. Fill out the registration form with farming details
2. Pay the one-time registration fee of ₹4,725
3. Receive confirmation and membership details
4. Get contacted by agricultural expert within 24 hours
5. Start receiving personalized farming support and benefits

Contact Information:
- Address: #2 Totad building, Arjun Vihar Gokul road Hubli, Karnataka 580030, India
- Phone: +91 99868 90777
- Email: <EMAIL>
- Website: https://darvigroup.in
- Working Hours: Monday - Saturday, 9:00 AM - 6:00 PM

Success Stories:
- Commercial Orchard Development: 50-acre commercial orchard in North Karnataka
- Smart Farming Implementation: 25-acre mixed fruit plantation, reduced water consumption by 30%
- Sustainable Agroforestry Project: 100-acre sustainable plantation with 15 small-scale farmers

Important Guidelines:
1. Focus on the Farmer Registration Program (₹4,725 lifetime membership) as the main offering
2. Explain the comprehensive benefits included in the registration program
3. Do NOT mention individual product sales (Genex, Neem, Santica) as standalone purchases
4. Products are available as part of membership benefits, not sold separately
5. Always encourage users to register for the program to get personalized consultation
6. Be helpful and knowledgeable about sustainable farming practices
7. Respond in a friendly, professional manner
8. If you don't know something specific, suggest contacting the company directly
9. Emphasize the value of expert consultation and personalized agricultural guidance
10. Keep responses CONCISE and STRUCTURED - use bullet points, short paragraphs
11. Always format responses with bullet points for easy reading
12. Include contact information (phone/email) when relevant
13. Maximum 3-4 bullet points per response unless specifically asked for details
14. Use emojis sparingly for better visual appeal
15. Structure responses as: Brief intro + bullet points + contact info (if needed)
`;

// Current business information for context
const CURRENT_BUSINESS_INFO = `
Darvi Group - Current Business Model (2024):

Main Service: Farmer Registration Program
- Price: ₹4,725 (one-time lifetime membership)
- Includes: Agricultural consultation, crop recommendations, soil assessment, expert support, IoT trial

What's Included:
1. Free Agricultural Consultation - Personalized consultation with agricultural experts
2. Customized Crop Recommendations - Based on soil analysis and local climate
3. Soil Health Assessment - Professional evaluation and improvement recommendations
4. Expert Support & Guidance - 24/7 access to agricultural experts
5. Access to Premium Products - Exclusive access at member prices
6. IoT Solutions Trial - Free trial of smart farming solutions

Registration Process:
1. Fill registration form → 2. Pay ₹4,725 → 3. Get confirmation → 4. Expert contact within 24hrs → 5. Start benefits

Contact: +91 99868 90777 | <EMAIL> | Hubli, Karnataka
`;

class GeminiService {
  private apiKey?: string;
  private apiUrl?: string;
  private isConfigured: boolean;
  private conversationHistory: { role: string; parts: { text: string }[] }[];
  private responseCache: Map<string, string> = new Map();

  constructor() {
    this.apiKey = process.env.REACT_APP_GEMINI_API_KEY;
    this.apiUrl = process.env.REACT_APP_GEMINI_API_URL;

    // Check if API key and URL are properly configured
    this.isConfigured = !!(this.apiKey && this.apiUrl);

    // Debug logging handled by logger utility
    if (process.env.NODE_ENV === 'development') {
      import('../utils/logger').then(logger => {
        logger.debug('Gemini Service Configuration:', {
          hasApiKey: !!this.apiKey,
          hasApiUrl: !!this.apiUrl,
          isConfigured: this.isConfigured
        });
      });
    }
    this.conversationHistory = [
      {
        role: 'user',
        parts: [{ text: DARVI_CONTEXT }]
      },
      {
        role: 'model',
        parts: [{ text: 'I understand. I am Darvi Assistant, ready to help with agricultural solutions and information about Darvi Group\'s Farmer Registration Program. How can I assist you today?' }]
      },
      {
        role: 'user',
        parts: [{ text: CURRENT_BUSINESS_INFO }]
      },
      {
        role: 'model',
        parts: [{ text: 'Thank you for the current business information. I now understand that Darvi Group offers a comprehensive Farmer Registration Program for ₹4,725 with lifetime membership benefits. I\'m ready to help customers learn about this program and agricultural consultation services.' }]
      }
    ];
  }

  // Only provide quick responses for very basic contact info
  private getQuickResponse(userMessage: string): string | null {
    const message = userMessage.toLowerCase().trim();

    // Only handle very basic contact requests
    if (message === 'contact' || message === 'phone' || message === 'number') {
      return `**Contact Darvi Group:**\n\n• Phone: +91 99868 90777\n• Email: <EMAIL>\n• Location: Hubli, Karnataka`;
    }

    return null; // Let AI handle everything else properly
  }

  async generateResponse(userMessage: string): Promise<string> {
    if (!this.apiKey || !this.apiUrl) {
      return `Hi! I'm here to help with Darvi Group services.

**Farmer Registration Program - ₹4,725 (Lifetime)**
• Free agricultural consultation
• Customized crop recommendations
• Soil health assessment
• Expert support & IoT trial

📞 +91 99868 90777 | ✉️ <EMAIL>

What would you like to know about our services?`;
    }

    if (!this.isConfigured) {
      return `Hello! I can help with Darvi Group information.

**Our Main Service:**
• Farmer Registration Program - ₹4,725
• Includes consultation & expert support

📞 Contact: +91 99868 90777 for assistance.`;
    }

    // Only use quick responses for very basic contact requests
    const quickResponse = this.getQuickResponse(userMessage);
    if (quickResponse) {
      return quickResponse;
    }

    // Use light caching only for exact same questions
    const cacheKey = userMessage.toLowerCase().trim();
    if (this.responseCache.has(cacheKey)) {
      return this.responseCache.get(cacheKey)!;
    }

    try {
      // Add user message to conversation history
      this.conversationHistory.push({
        role: 'user',
        parts: [{ text: userMessage }]
      });

      // Comprehensive system instruction with full website content
      const systemInstruction = `You are Darvi Group's helpful AI assistant. Provide accurate, detailed information about our company and services.

COMPANY OVERVIEW:
Darvi Group - Founded in 2018, based in Hubli, Karnataka. We are a B2B and B2C provider of premium agricultural solutions specializing in sustainable farming practices and valuable timber species like sandalwood.

OUR TEAM:
• **Nithinkumar** - CEO: BSc (Forestry). Passionate advocate for sustainable agriculture and forestry practices. Leads the company's vision and strategic direction with expertise in forestry and sustainable agriculture.

• **Sachinkumar** - Co-founder: Environmental Studies, Geography and Commercial Forestry. Expert in commercial forestry and agroforestry models. Specializes in sandalwood cultivation and land development with experience in different commercial forest crops and managed farmland agroforestry models.

• **Naveenkumar** - Marketing Head: B.Sc Computer Application, MBA (Marketing). Strategic marketing professional with expertise in agricultural solutions and marketing operations. Has experience in integrated applications on security systems.

• **Shareeshail B** - Technical Head: BSc & MSc (Forestry). Forestry expert specializing in forest product utilization, essential oil extraction and analysis. Expert in crop health and agroforestry models with experience in different commercial forest crops and soil/crop health monitoring. Currently working on sandalwood host, growth, size and yield based on different soil and climatic conditions.

• **Sushma TM** - Technical Head: BSc (Horticulture), MSc (Agriculture). Horticulture specialist focused on organic farming practices. Former Technical Assistant in Karnataka Horticulture Department with experience in different commercial horticulture crops. Currently working on effectiveness of organic fertilizers on crop growth, health, yield and quality.

MAIN SERVICE - FARMER REGISTRATION PROGRAM (₹4,725):
• Free Agricultural Consultation - Personalized consultation with agricultural experts
• Customized Crop Recommendations - Based on soil analysis and local climate conditions
• Soil Health Assessment - Professional soil health evaluation and improvement recommendations
• Expert Support & Guidance - 24/7 access to agricultural experts for ongoing support
• Access to Premium Products - Exclusive access to agricultural products at member prices
• IoT Solutions Trial - Free trial of IoT-based smart farming solutions for crop monitoring

AGRICULTURAL PRODUCTS:
• **Genex** - Organic soil conditioner for improved soil health and fertility
• **Neem** - Natural pest control solution for sustainable farming
• **Santica** - Plant growth enhancer for better crop yields

SERVICES OFFERED:
• Agricultural consultation and expert advice
• Sustainable farming practices implementation
• IoT-powered agriculture monitoring and smart farming solutions
• Land management and optimization advice
• Government scheme assistance for farmer registration
• Crop planning and management guidance
• Soil health assessment and improvement
• Pest and disease management solutions

CONTACT INFORMATION:
• Phone: +91 99868 90777
• Email: <EMAIL>
• Address: #2 Totad building, Arjun Vihar Gokul road Hubli, Karnataka 580030, India
• Working Hours: Monday - Saturday, 9:00 AM - 6:00 PM
• Website: https://darvigroup.in
• Online Store: https://mybillbook.in/store/darvi_group
• IoT Portal: https://iot.haegl.in

COMPANY VALUES:
• Sustainability - Environmentally responsible practices
• Community - Supporting local farming communities
• Innovation - Exploring new technologies and methods
• Growth - Sustainable growth for clients and partners

RESPONSE GUIDELINES:
- Provide detailed, helpful answers about any aspect of Darvi Group
- Use bullet points for clarity
- Include contact information when relevant
- Be professional and informative
- Answer questions about team members, services, products, registration process, etc.
- Don't just redirect to contact - give comprehensive information first

Always provide complete, helpful answers about Darvi Group's services, team, products, and agricultural solutions.`;

      // Include conversation context for better responses
      const requestData = {
        contents: [
          {
            role: 'user',
            parts: [{ text: systemInstruction + '\n\nUser question: ' + userMessage }]
          },
          ...this.conversationHistory.slice(-6) // Keep recent context for better answers
        ],
        generationConfig: {
          maxOutputTokens: 300, // Allow longer, more helpful responses
          temperature: 0.4,     // Balanced for helpful but focused responses
          topP: 0.9,
          topK: 40
        }
      };



      // Optimized API call with timeout for faster responses
      const response = await axios.post(
        `${this.apiUrl}?key=${this.apiKey}`,
        requestData,
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 8000, // 8 second timeout for faster failure
          validateStatus: (status) => status < 500 // Accept 4xx errors
        }
      );



      if (response.data.candidates && response.data.candidates.length > 0) {
        let aiResponse = response.data.candidates[0].content.parts[0].text;

        // Format the response to ensure proper bullet points and highlighting
        aiResponse = this.formatResponse(aiResponse);

        // Cache the response for faster future access
        this.responseCache.set(cacheKey, aiResponse);

        // Limit cache size to prevent memory issues
        if (this.responseCache.size > 50) {
          const firstKey = this.responseCache.keys().next().value;
          if (firstKey) {
            this.responseCache.delete(firstKey);
          }
        }

        // Add AI response to conversation history for context
        this.conversationHistory.push({
          role: 'model',
          parts: [{ text: aiResponse }]
        });

        // Keep conversation history manageable but useful
        if (this.conversationHistory.length > 10) {
          this.conversationHistory = [
            this.conversationHistory[0], // Keep initial context
            ...this.conversationHistory.slice(-6) // Keep recent conversation
          ];
        }

        return aiResponse;
      }

      // If Gemini API fails, return a helpful general response
      return `Hi! I'm here to help with Darvi Group's agricultural services.

**Farmer Registration Program - ₹4,725 (Lifetime)**
• Free agricultural consultation
• Customized crop recommendations
• Soil health assessment
• Expert support & IoT trial

📞 +91 99868 90777 | ✉️ <EMAIL>

What would you like to know?`;
    } catch (error) {
      // Silent error handling for production

      // Even if there's an error, try to provide a helpful response
      return `Sorry for the technical issue! Here's quick info about Darvi Group:

**Farmer Registration - ₹4,725 (Lifetime)**
• Agricultural consultation & crop advice
• Soil health assessment
• Expert support & IoT trial

📞 +91 99868 90777 | ✉️ <EMAIL>`;
    }
  }

  private formatResponse(response: string): string {
    // Ensure bullet points are properly formatted
    response = response.replace(/\n\s*[-•]\s*/g, '\n• ');

    // Ensure prices are highlighted
    response = response.replace(/(₹\d+,\d+|\d+,\d+)/g, '**$1**');

    // Ensure links are properly formatted
    response = response.replace(/(https?:\/\/[^\s]+)/g, '[$1]($1)');

    return response;
  }

  // Method to check if a query is related to Darvi Group or agriculture
  isRelevantQuery(query: string): boolean {
    const relevantKeywords = [
      // Company related
      'darvi', 'group', 'company', 'business', 'organization', 'firm',

      // Agriculture related
      'agriculture', 'farming', 'crop', 'plant', 'soil', 'irrigation', 'field',
      'sustainable', 'organic', 'fertilizer', 'pesticide', 'harvest', 'seed',
      'farmer', 'land', 'cultivation', 'grow', 'farm', 'forestry', 'garden',
      'yield', 'produce', 'growth', 'planting', 'sowing', 'agricultural',

      // IoT related
      'iot', 'monitoring', 'sensor', 'technology', 'smart', 'device', 'system',
      'data', 'tracking', 'automation', 'digital', 'tech', 'solution',

      // Products and services
      'product', 'service', 'genex', 'neem', 'santica', 'consultation',
      'registration', 'management', 'advice', 'support', 'help', 'assist',

      // Business related
      'contact', 'location', 'price', 'cost', 'payment', 'buy', 'purchase',
      'order', 'delivery', 'shipping', 'store', 'shop', 'website', 'online',

      // Expertise related
      'expert', 'advice', 'guidance', 'recommendation', 'suggestion', 'tip',
      'information', 'knowledge', 'experience', 'skill', 'specialty',

      // Location related
      'Karnataka', 'India', 'Hubli', 'address', 'office', 'headquarters',

      // Common questions
      'what', 'how', 'when', 'where', 'why', 'who', 'which', 'can', 'do', 'does'
    ];

    // If the query is very short (less than 4 characters), consider it relevant
    // to avoid filtering out short queries like "IoT" or "help"
    if (query.trim().length < 4) return true;

    const queryLower = query.toLowerCase();

    // Check if any of the keywords are in the query
    return relevantKeywords.some(keyword => queryLower.includes(keyword.toLowerCase()));
  }
}

// Create and export a singleton instance
const geminiService = new GeminiService();

// Gemini service is ready to use

export default geminiService;
