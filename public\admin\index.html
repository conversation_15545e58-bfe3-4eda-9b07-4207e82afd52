<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Darvi Group Content Manager</title>

    <!-- Content Security Policy for Netlify CMS Admin -->
    <meta http-equiv="Content-Security-Policy" content="
      default-src 'self';
      script-src 'self' 'unsafe-inline' 'unsafe-eval' https://secure.payu.in https://test.payu.in https://*.payu.in https://identity.netlify.com https://www.googletagmanager.com https://www.google-analytics.com https://unpkg.com https://cdn.jsdelivr.net https://*.netlify.com https://*.netlify.app https://*.cloudinary.com https://api.cloudinary.com https://darvigroup.in;
      style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com https://cdn.jsdelivr.net;
      font-src 'self' https://fonts.gstatic.com https://unpkg.com https://cdn.jsdelivr.net;
      img-src 'self' data: https: blob: https://*.cloudinary.com https://darvigroup.in;
      connect-src 'self' https://darvigroup.in https://secure.payu.in https://test.payu.in https://*.payu.in https://info.payu.in https://script.google.com https://script.googleusercontent.com https://www.google-analytics.com https://analytics.google.com https://formsubmit.co https://api.emailjs.com https://api.web3forms.com https://api.netlify.com https://*.netlify.com https://*.netlify.app https://api.github.com https://uploads.github.com https://generativelanguage.googleapis.com https://api.weatherapi.com https://*.cloudinary.com https://api.cloudinary.com;
      frame-src 'self' https://secure.payu.in https://test.payu.in https://*.payu.in https://identity.netlify.com https://*.netlify.com https://*.netlify.app https://www.google.com;
      object-src 'none';
      base-uri 'self';
      form-action 'self' https://secure.payu.in/_payment https://test.payu.in/_payment https://secure.payu.in https://test.payu.in https://*.payu.in https://api.netlify.com;
    " />

    <script src="https://identity.netlify.com/v1/netlify-identity-widget.js"></script>
  </head>
  <body>
    <!-- Include the script that builds the page and powers Netlify CMS -->
    <script src="https://unpkg.com/netlify-cms@2.10.192/dist/netlify-cms.js"></script>

    <script>
      // Initialize Netlify Identity
      if (window.netlifyIdentity) {
        window.netlifyIdentity.on("init", user => {
          if (!user) {
            window.netlifyIdentity.on("login", () => {
              document.location.href = "/admin/";
            });
          }
        });
      }
    </script>
  </body>
</html>
