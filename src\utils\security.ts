/**
 * Security utilities for production deployment
 */
import config from '../config';

export class SecurityManager {
  /**
   * Enforce HTTPS in production environment
   */
  static enforceHTTPS(): void {
    if (config.security.enforceHTTPS && window.location.protocol !== 'https:') {
      // Redirecting to HTTPS for security
      window.location.replace(`https:${window.location.href.substring(window.location.protocol.length)}`);
    }
  }

  /**
   * Validate that we're running in a secure context
   */
  static validateSecureContext(): boolean {
    if (process.env.NODE_ENV === 'production') {
      return window.isSecureContext && window.location.protocol === 'https:';
    }
    return true; // Allow non-secure context in development
  }

  /**
   * Sanitize user input to prevent XSS attacks
   */
  static sanitizeInput(input: string): string {
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }

  /**
   * Validate form data before submission
   */
  static validateFormData(data: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Name validation
    if (!data.name || data.name.trim().length < 2) {
      errors.push('Name must be at least 2 characters long');
    }

    // Mobile validation (Indian mobile number format)
    if (!data.mobile || !/^[6-9]\d{9}$/.test(data.mobile)) {
      errors.push('Please enter a valid 10-digit mobile number');
    }

    // Address validation
    if (!data.address || data.address.trim().length < 10) {
      errors.push('Please provide a complete address');
    }

    // City validation
    if (!data.city || data.city.trim().length < 2) {
      errors.push('Please enter a valid city name');
    }

    // District validation
    if (!data.district || data.district.trim().length < 2) {
      errors.push('Please select a district');
    }

    // Taluk validation
    if (!data.taluk || data.taluk.trim().length < 2) {
      errors.push('Please enter a valid taluk');
    }

    // Land area validation
    if (!data.landArea || !/^[0-9]*\.?[0-9]+$/.test(data.landArea)) {
      errors.push('Please enter a valid land area');
    }

    // Soil type validation
    if (!data.soilType || data.soilType.trim().length < 2) {
      errors.push('Please select a soil type');
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * Mask sensitive data for logging
   */
  static maskSensitiveData(data: any): any {
    const masked = { ...data };

    // Mask mobile number
    if (masked.mobile) {
      masked.mobile = masked.mobile.replace(/(\d{2})\d{6}(\d{2})/, '$1******$2');
    }

    // Remove any transaction IDs from logs
    if (masked.transactionId) {
      masked.transactionId = '***MASKED***';
    }

    return masked;
  }

  /**
   * Check if current domain is whitelisted
   */
  static isDomainWhitelisted(): boolean {
    const currentOrigin = window.location.origin;
    return config.security.allowedOrigins.includes(currentOrigin) ||
           process.env.NODE_ENV === 'development';
  }

  /**
   * Generate a secure random string for client-side tracking
   */
  static generateSecureId(): string {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }



  /**
   * Initialize security measures on app startup
   */
  static initialize(): void {
    // Enforce HTTPS
    this.enforceHTTPS();

    // Validate secure context
    if (!this.validateSecureContext()) {
      // Application must run in a secure context (HTTPS) in production
    }

    // Check domain whitelist
    if (!this.isDomainWhitelisted()) {
      // Current domain is not whitelisted for production use
    }

    // Security manager initialized
  }
}

/**
 * Data protection utilities
 */
export class DataProtection {
  /**
   * Encrypt sensitive data before storing locally
   */
  static encryptData(data: string, key: string): string {
    // Simple XOR encryption for client-side (not for highly sensitive data)
    let encrypted = '';
    for (let i = 0; i < data.length; i++) {
      encrypted += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i % key.length));
    }
    return btoa(encrypted);
  }

  /**
   * Decrypt data from local storage
   */
  static decryptData(encryptedData: string, key: string): string {
    try {
      const data = atob(encryptedData);
      let decrypted = '';
      for (let i = 0; i < data.length; i++) {
        decrypted += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i % key.length));
      }
      return decrypted;
    } catch (error) {
      // Failed to decrypt data
      return '';
    }
  }

  /**
   * Secure local storage operations
   */
  static setSecureItem(key: string, value: any): void {
    try {
      const serialized = JSON.stringify(value);
      const encrypted = this.encryptData(serialized, key);
      localStorage.setItem(key, encrypted);
    } catch (error) {
      // Failed to store secure item
    }
  }

  /**
   * Retrieve and decrypt from local storage
   */
  static getSecureItem(key: string): any {
    try {
      const encrypted = localStorage.getItem(key);
      if (!encrypted) return null;

      const decrypted = this.decryptData(encrypted, key);
      return JSON.parse(decrypted);
    } catch (error) {
      // Failed to retrieve secure item
      return null;
    }
  }

  /**
   * Clear sensitive data from local storage
   */
  static clearSensitiveData(): void {
    const sensitiveKeys = [
      'darvi_payment_state',
      'currentTransactionId',
      'customerName'
    ];

    sensitiveKeys.forEach(key => {
      localStorage.removeItem(key);
    });
  }
}

// Initialize security on module load
if (typeof window !== 'undefined') {
  SecurityManager.initialize();
}
