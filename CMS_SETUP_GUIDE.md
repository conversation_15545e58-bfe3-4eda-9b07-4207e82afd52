# Darvi Group CMS Setup Guide

## Overview

This guide provides comprehensive information about the Content Management System (CMS) setup for the Darvi Group agricultural website. The CMS is built using Netlify CMS with enhanced image management, content validation, and multi-language support.

## Features

### ✅ Complete CMS Setup
- **Netlify CMS Integration**: Full configuration with git-gateway backend
- **Image Management**: Advanced image upload, optimization, and management
- **Content Validation**: Real-time validation with error reporting
- **Multi-language Support**: English, Kannada, and Hindi content management
- **Responsive Images**: Automatic generation of multiple image sizes
- **Content Editor**: Rich content editing interface with preview capabilities

### ✅ Image Management
- **Upload Interface**: Drag-and-drop image upload with preview
- **Image Optimization**: Automatic compression and resizing
- **Format Support**: JPEG, PNG, GIF, WebP, SVG
- **Validation**: File size, format, and dimension validation
- **Responsive Images**: Automatic generation of thumbnail, medium, and large sizes
- **Alt Text Management**: Required alt text and title fields for accessibility

### ✅ Content Types Supported
- **Home Page**: Hero sections, features, testimonials, gallery
- **About Page**: Company information, team details, values
- **Services Page**: Service offerings, descriptions, contact info
- **Blog Posts**: Articles with categories, tags, and featured images
- **IoT Solutions**: Product features, specifications, documentation
- **Contact Page**: Contact forms, location, business hours
- **Legal Pages**: Privacy policy, terms of service, refund policy

## File Structure

```
src/
├── components/
│   ├── ImageUpload.tsx          # Image upload component with preview/edit
│   ├── ContentEditor.tsx        # Rich content editor with validation
│   ├── ContentValidator.tsx     # Content validation and scoring
│   └── CMSAdmin.tsx            # Enhanced CMS admin interface
├── utils/
│   └── imageOptimization.ts    # Image processing utilities
├── content/                    # JSON content files
│   ├── home/                   # Home page content
│   ├── about/                  # About page content
│   ├── services/               # Services content
│   ├── blog/                   # Blog posts
│   ├── iot/                    # IoT solutions content
│   ├── contact/                # Contact page content
│   ├── ui/                     # UI text and labels
│   └── layout/                 # Header/footer content
├── config/                     # Configuration files
│   ├── cms-settings.json       # CMS configuration
│   ├── payment-settings.json   # Payment gateway settings
│   ├── features.json           # Feature flags
│   └── form-settings.json      # Form configurations
└── tests/
    └── cms.test.tsx            # CMS functionality tests
```

## Configuration Files

### Netlify CMS Configuration (`public/admin/config.yml`)
- **Backend**: Git-gateway with GitHub integration
- **Media Library**: Cloudinary integration for image hosting
- **Collections**: Comprehensive content type definitions
- **Image Optimization**: Automatic image processing settings
- **Validation**: Field validation rules and requirements

### Content Structure
All content is stored in JSON files with the following structure:
```json
{
  "hero": {
    "title": {
      "en": "English Title",
      "kn": "ಕನ್ನಡ ಶೀರ್ಷಿಕೆ",
      "hi": "हिंदी शीर्षक"
    },
    "image": "/darvi-images/hero-image.jpg"
  }
}
```

## Usage Instructions

### Accessing the CMS
1. **Admin Interface**: Navigate to `/admin` on your website
2. **Authentication**: Login with GitHub credentials (configured in Netlify)
3. **Content Management**: Use the enhanced admin interface at `/cms-admin`

### Managing Content
1. **Select Content Type**: Choose from Home, About, Services, Blog, etc.
2. **Edit Content**: Use the rich content editor with real-time validation
3. **Upload Images**: Drag and drop images with automatic optimization
4. **Preview Changes**: Use the preview functionality before publishing
5. **Publish**: Save changes to update the live website

### Image Management
1. **Upload**: Drag and drop images or click to browse
2. **Edit Metadata**: Add titles, alt text, and descriptions
3. **Categorize**: Assign images to categories (hero, gallery, team, etc.)
4. **Optimize**: Images are automatically compressed and resized
5. **Responsive**: Multiple sizes generated for different screen sizes

### Content Validation
- **Real-time Validation**: Content is validated as you type
- **Error Reporting**: Clear error messages with fix suggestions
- **Validation Score**: Overall content quality score
- **Required Fields**: Highlighted required fields with validation

## Technical Implementation

### Components

#### ImageUpload Component
- **Features**: Drag-and-drop, preview, edit, delete
- **Validation**: File type, size, dimensions
- **Optimization**: Automatic compression and resizing
- **Accessibility**: Alt text and title requirements

#### ContentEditor Component
- **Multi-language**: Tabbed interface for different languages
- **Field Types**: Text, images, lists, objects
- **Validation**: Real-time validation with error highlighting
- **Preview**: Live preview of content changes

#### ContentValidator Component
- **Validation Rules**: Configurable validation rules
- **Scoring System**: Content quality scoring
- **Error Reporting**: Detailed error messages and suggestions
- **Progress Tracking**: Visual progress indicators

### Image Optimization
- **Automatic Compression**: Reduces file sizes while maintaining quality
- **Multiple Formats**: Supports JPEG, PNG, WebP conversion
- **Responsive Images**: Generates thumbnail, medium, large sizes
- **Metadata Extraction**: Extracts image dimensions and metadata

## Testing

### Running Tests
```bash
npm test src/tests/cms.test.tsx
```

### Test Coverage
- **Component Testing**: All CMS components tested
- **Image Upload**: File validation and upload functionality
- **Content Validation**: Validation rules and error handling
- **Integration**: End-to-end CMS functionality

## Deployment

### Prerequisites
1. **Netlify Account**: Set up Netlify hosting
2. **GitHub Repository**: Connect repository to Netlify
3. **Cloudinary Account**: For image hosting (optional)
4. **Environment Variables**: Configure API keys

### Environment Variables
```
CLOUDINARY_API_KEY=your_cloudinary_api_key
NETLIFY_SITE_ID=your_netlify_site_id
```

### Build Configuration
```json
{
  "build": {
    "command": "npm run build",
    "publish": "build"
  }
}
```

## Maintenance

### Regular Tasks
1. **Content Backup**: Regular backup of content files
2. **Image Optimization**: Monitor image sizes and optimize as needed
3. **Validation Rules**: Update validation rules as content requirements change
4. **Security Updates**: Keep dependencies updated

### Monitoring
- **Content Quality**: Monitor validation scores
- **Image Performance**: Track image loading times
- **User Experience**: Monitor CMS usage and feedback

## Support

### Common Issues
1. **Image Upload Fails**: Check file size and format requirements
2. **Validation Errors**: Review required fields and content guidelines
3. **Preview Not Working**: Ensure all required fields are filled
4. **Save Failures**: Check network connection and permissions

### Getting Help
- **Documentation**: Refer to this guide and component documentation
- **Testing**: Run tests to identify issues
- **Logs**: Check browser console for error messages
- **Support**: Contact development team for technical issues

## Future Enhancements

### Planned Features
- **Advanced Image Editing**: In-browser image editing tools
- **Content Scheduling**: Schedule content publication
- **Workflow Management**: Content approval workflows
- **Analytics Integration**: Content performance tracking
- **SEO Optimization**: Enhanced SEO tools and validation

This CMS setup provides a comprehensive, user-friendly content management system with advanced image handling, validation, and multi-language support for the Darvi Group website.
