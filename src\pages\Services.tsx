import React from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  
  Button
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import IconComponent from '../utils/iconUtils';
import DarviLogo from '../components/DarviLogo';
import servicesContent from '../content/services/content.json';

const Services = () => {

  

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section */}
      <Box sx={{ textAlign: 'center', mb: 6 }}>
        <Box sx={{ mb: 3 }}>
          <DarviLogo variant="banner" />
        </Box>
        <Typography
          variant="h2"
          component="h1"
          gutterBottom
          sx={{
            fontWeight: 700,
            background: 'linear-gradient(45deg, #1B4C35 30%, #4CAF50 90%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mb: 2
          }}
        >
          {servicesContent.hero.title}
        </Typography>
        <Typography variant="h5" color="text.secondary" paragraph sx={{ maxWidth: '800px', mx: 'auto' }}>
          {servicesContent.hero.subtitle}
        </Typography>
      </Box>







      {/* Design Services Section */}
      <Box sx={{ mb: 8 }}>
        <Typography variant="h4" gutterBottom align="center" sx={{ mb: 6, fontWeight: 600 }}>
          {servicesContent.sections.designTitle}
        </Typography>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Card elevation={3} sx={{ height: '100%', borderRadius: '16px', overflow: 'hidden' }}>
              <Box
                sx={{
                  height: 240,
                  backgroundImage: 'url(/darvi-images/landscape.jpg)',
                  backgroundSize: 'cover',
                  backgroundPosition: 'center'
                }}
              />
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h5" gutterBottom color="primary" fontWeight={600}>
                  {servicesContent.designSection.title}
                </Typography>
                <Typography variant="body1" paragraph>
                  {servicesContent.designSection.description}
                </Typography>
                <Box component="ul" sx={{ pl: 2 }}>
                  {servicesContent.designSection.features.map((feature, index) => (
                    <Typography 
                      key={index} 
                      component="li" 
                      variant="body1" 
                      sx={{ 
                        mb: 1,
                        display: 'flex',
                        alignItems: 'flex-start'
                      }}
                    >
                      <CheckCircleIcon sx={{ color: 'success.main', fontSize: 20, mr: 1, mt: 0.5 }} />
                      {feature}
                    </Typography>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card elevation={3} sx={{ height: '100%', borderRadius: '16px', overflow: 'hidden' }}>
              <Box
                sx={{
                  height: 240,
                  backgroundImage: 'url(/darvi-images/land2.jpg)',
                  backgroundSize: 'cover',
                  backgroundPosition: 'center'
                }}
              />
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h5" gutterBottom color="primary" fontWeight={600}>
                  Landscape Design
                </Typography>
                <Typography variant="body1" paragraph>
                  Our expert designers create beautiful, functional, and sustainable landscapes for your property.
                </Typography>
                <Box component="ul" sx={{ pl: 2 }}>
                  <Typography 
                    component="li" 
                    variant="body1" 
                    sx={{ 
                      mb: 1,
                      display: 'flex',
                      alignItems: 'flex-start'
                    }}
                  >
                    <CheckCircleIcon sx={{ color: 'success.main', fontSize: 20, mr: 1, mt: 0.5 }} />
                    Professional landscape architecture
                  </Typography>
                  <Typography 
                    component="li" 
                    variant="body1" 
                    sx={{ 
                      mb: 1,
                      display: 'flex',
                      alignItems: 'flex-start'
                    }}
                  >
                    <CheckCircleIcon sx={{ color: 'success.main', fontSize: 20, mr: 1, mt: 0.5 }} />
                    Sustainable and eco-friendly designs
                  </Typography>
                  <Typography 
                    component="li" 
                    variant="body1" 
                    sx={{ 
                      mb: 1,
                      display: 'flex',
                      alignItems: 'flex-start'
                    }}
                  >
                    <CheckCircleIcon sx={{ color: 'success.main', fontSize: 20, mr: 1, mt: 0.5 }} />
                    Water-efficient irrigation planning
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                  <Button 
                    variant="outlined" 
                    color="primary"
                    endIcon={<ArrowForwardIcon />}
                    component={RouterLink}
                    to="/contact"
                  >
                    Request Design Consultation
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>



      {/* Dairy Farm Section */}
      <Box sx={{ mb: 8 }}>
        <Typography variant="h4" gutterBottom align="center" sx={{ mb: 6, fontWeight: 600 }}>
          {servicesContent.sections.dairyFarmTitle}
        </Typography>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Card elevation={3} sx={{ height: '100%', borderRadius: '16px', overflow: 'hidden' }}>
              <Box
                sx={{
                  height: 240,
                  backgroundImage: 'url(/darvi-images/dairy1.jpg)',
                  backgroundSize: 'cover',
                  backgroundPosition: 'center'
                }}
              />
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h5" gutterBottom color="primary" fontWeight={600}>
                  {servicesContent.dairyFarmSection.title}
                </Typography>
                <Typography variant="body1" paragraph>
                  {servicesContent.dairyFarmSection.description}
                </Typography>
                <Box component="ul" sx={{ pl: 2 }}>
                  {servicesContent.dairyFarmSection.features.map((feature, index) => (
                    <Typography 
                      key={index} 
                      component="li" 
                      variant="body1" 
                      sx={{ 
                        mb: 1,
                        display: 'flex',
                        alignItems: 'flex-start'
                      }}
                    >
                      <CheckCircleIcon sx={{ color: 'success.main', fontSize: 20, mr: 1, mt: 0.5 }} />
                      {feature}
                    </Typography>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card elevation={3} sx={{ height: '100%', borderRadius: '16px', overflow: 'hidden' }}>
              <Box
                sx={{
                  height: 240,
                  backgroundImage: 'url(/darvi-images/dairy2.jpg)',
                  backgroundSize: 'cover',
                  backgroundPosition: 'center'
                }}
              />
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h5" gutterBottom color="primary" fontWeight={600}>
                  Managed Milk Farm
                </Typography>
                <Typography variant="body1" paragraph>
                  Our dairy farm management services help you optimize milk production while maintaining the highest standards of animal welfare.
                </Typography>
                <Box component="ul" sx={{ pl: 2 }}>
                  <Typography 
                    component="li" 
                    variant="body1" 
                    sx={{ 
                      mb: 1,
                      display: 'flex',
                      alignItems: 'flex-start'
                    }}
                  >
                    <CheckCircleIcon sx={{ color: 'success.main', fontSize: 20, mr: 1, mt: 0.5 }} />
                    Advanced cattle health monitoring systems
                  </Typography>
                  <Typography 
                    component="li" 
                    variant="body1" 
                    sx={{ 
                      mb: 1,
                      display: 'flex',
                      alignItems: 'flex-start'
                    }}
                  >
                    <CheckCircleIcon sx={{ color: 'success.main', fontSize: 20, mr: 1, mt: 0.5 }} />
                    Optimized feed management programs
                  </Typography>
                  <Typography 
                    component="li" 
                    variant="body1" 
                    sx={{ 
                      mb: 1,
                      display: 'flex',
                      alignItems: 'flex-start'
                    }}
                  >
                    <CheckCircleIcon sx={{ color: 'success.main', fontSize: 20, mr: 1, mt: 0.5 }} />
                    Quality milk production techniques
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                  <Button 
                    variant="outlined" 
                    color="primary"
                    endIcon={<ArrowForwardIcon />}
                    component={RouterLink}
                    to="/contact"
                  >
                    Learn More
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* Contact Information */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom align="center" sx={{ mb: 4 }}>
          {servicesContent.sections.contactTitle}
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card elevation={2} sx={{ textAlign: 'center', p: 3 }}>
              <Box sx={{ mb: 2 }}>
                <IconComponent name="phone" size={40} />
              </Box>
              <Typography variant="h6" gutterBottom>
                {servicesContent.contactLabels.phone}
              </Typography>
              <Typography variant="body1" color="primary">
                {servicesContent.contactInfo.phone}
              </Typography>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card elevation={2} sx={{ textAlign: 'center', p: 3 }}>
              <Box sx={{ mb: 2 }}>
                <IconComponent name="email" size={40} />
              </Box>
              <Typography variant="h6" gutterBottom>
                {servicesContent.contactLabels.email}
              </Typography>
              <Typography variant="body1" color="primary">
                {servicesContent.contactInfo.email}
              </Typography>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card elevation={2} sx={{ textAlign: 'center', p: 3 }}>
              <Box sx={{ mb: 2 }}>
                <IconComponent name="location" size={40} />
              </Box>
              <Typography variant="h6" gutterBottom>
                {servicesContent.contactLabels.address}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {servicesContent.contactInfo.address}
              </Typography>
            </Card>
          </Grid>
        </Grid>

        <Box sx={{ textAlign: 'center', mt: 3 }}>
          <Typography variant="body1" color="text.secondary">
            <strong>{servicesContent.workingHoursLabel}:</strong> {servicesContent.contactInfo.workingHours}
          </Typography>
        </Box>
      </Box>
    </Container>
  );
};

export default Services;
