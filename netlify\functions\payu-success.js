/**
 * PayU Payment Success Handler
 * Converted from PHP success.php to JavaScript Netlify Function
 * Handles successful payment responses from PayU
 */

// Import utilities
const { getPayUConfig } = require('./utils/payuConfig');
const { verifyPayUHash } = require('./utils/hashGenerator');
const { validatePayUResponse, sanitizePayUResponse } = require('./utils/payuValidator');
const { logPaymentResponse, logError, debugLog } = require('./utils/logger');

// CORS headers
const getCORSHeaders = (origin) => {
  const allowedOrigins = process.env.NODE_ENV === 'production' 
    ? ['https://darvigroup.in', 'https://www.darvigroup.in']
    : ['http://localhost:3000', 'http://127.0.0.1:3000', 'https://localhost:3000'];
  
  const isAllowedOrigin = allowedOrigins.includes(origin);
  
  return {
    'Access-Control-Allow-Origin': isAllowedOrigin ? origin : allowedOrigins[0],
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Content-Type': 'application/json',
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block'
  };
};

// Main handler
exports.handler = async (event) => {
  debugLog('PayU success handler called', {
    method: event.httpMethod,
    hasBody: !!event.body,
    hasQueryParams: !!event.queryStringParameters
  });

  const origin = event.headers.origin || event.headers.Origin;
  const corsHeaders = getCORSHeaders(origin);

  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    const config = getPayUConfig();
    let responseData = {};

    // Parse PayU response data (can come via POST or GET)
    if (event.httpMethod === 'POST' && event.body) {
      try {
        // Try to parse as JSON first
        responseData = JSON.parse(event.body);
      } catch (jsonError) {
        // If not JSON, try to parse as form data
        const formData = new URLSearchParams(event.body);
        responseData = Object.fromEntries(formData.entries());
      }
    } else if (event.queryStringParameters) {
      // Handle GET request with query parameters
      responseData = event.queryStringParameters;
    }

    debugLog('PayU response data received', {
      txnid: responseData.txnid,
      status: responseData.status,
      amount: responseData.amount,
      mihpayid: responseData.mihpayid
    });

    // Validate PayU response
    const validation = validatePayUResponse(responseData);
    if (!validation.isValid) {
      logError('PayU response validation failed', { errors: validation.errors });
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({
          success: false,
          message: 'Invalid PayU response',
          errors: validation.errors
        })
      };
    }

    // Sanitize response data
    const sanitized = sanitizePayUResponse(responseData);

    // Extract key parameters
    const {
      mihpayid = '',
      mode = '',
      status = '',
      key = '',
      txnid = '',
      amount = '',
      productinfo = '',
      firstname = '',
      lastname = '',
      email = '',
      phone = '',
      hash = '',
      error = '',
      error_Message = '',
      bank_ref_num = '',
      bankcode = '',
      cardnum = '',
      name_on_card = '',
      udf1 = '',
      udf2 = '',
      udf3 = '',
      udf4 = '',
      udf5 = ''
    } = sanitized;

    // Verify hash for security
    const hashVerification = verifyPayUHash(
      {
        key,
        txnid,
        amount,
        productinfo,
        firstname,
        email,
        status,
        udf1,
        udf2,
        udf3,
        udf4,
        udf5
      },
      hash,
      config.salt
    );

    const hashVerified = hashVerification.isValid;

    // Log the transaction response
    logPaymentResponse(sanitized, hashVerified);

    // Prepare response data
    const transactionDetails = {
      txnid,
      mihpayid,
      status,
      amount,
      mode,
      firstname,
      lastname,
      email,
      phone,
      productinfo,
      bank_ref_num,
      bankcode,
      cardnum: cardnum ? cardnum.replace(/\d(?=\d{4})/g, '*') : '', // Mask card number
      name_on_card,
      error,
      error_message: error_Message,
      hash_verified: hashVerified,
      timestamp: new Date().toISOString()
    };

    debugLog('Payment success processed', {
      txnid,
      status,
      hash_verified: hashVerified,
      amount
    });

    // Return success response
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        message: 'Payment processed successfully',
        data: {
          transaction: transactionDetails,
          security: {
            hash_verified: hashVerified,
            hash_message: hashVerification.message
          }
        }
      })
    };

  } catch (error) {
    logError('PayU success handler error', {
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });

    const errorMessage = process.env.NODE_ENV === 'production'
      ? 'Payment processing failed. Please contact support.'
      : error.message || 'Payment processing failed';

    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        message: errorMessage,
        timestamp: new Date().toISOString()
      })
    };
  }
};
