import React, { useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  Paper,
  Alert,
  CircularProgress
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import SecurityIcon from '@mui/icons-material/Security';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import darviData from '../data/darviContentData.json';

const Checkout = () => {

  const navigate = useNavigate();
  const { registrationServices } = darviData;
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // No form data needed since we're redirecting to the registration form

  const handleRegistration = async () => {
    setLoading(true);

    try {
      // Redirect directly to farmer registration form
      navigate('/form');
    } catch (err) {
      setError('Something went wrong. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const orderSummary = {
    item: 'Darvi Group Farmer Registration',
    price: registrationServices.registrationFee.amount,
    description: registrationServices.registrationFee.description
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom color="primary">
          <ShoppingCartIcon sx={{ mr: 2, fontSize: 'inherit' }} />
          Checkout
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Complete your registration
        </Typography>
      </Box>

      <Grid container spacing={4}>
        {/* Order Summary */}
        <Grid item xs={12} md={5}>
          <Card elevation={3} sx={{ borderRadius: '16px', position: 'sticky', top: 20 }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h5" gutterBottom fontWeight={600}>
                Order Summary
              </Typography>

              <Box sx={{ my: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body1" fontWeight={500}>
                    {orderSummary.item}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {orderSummary.description}
                </Typography>

                <Divider sx={{ my: 2 }} />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body1">Subtotal:</Typography>
                  <Typography variant="body1">₹{orderSummary.price.toLocaleString()}</Typography>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body1">Processing Fee:</Typography>
                  <Typography variant="body1" color="success.main">Free</Typography>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="h6" fontWeight={600}>Total:</Typography>
                  <Typography variant="h6" fontWeight={600} color="primary">
                    ₹{orderSummary.price.toLocaleString()}
                  </Typography>
                </Box>
              </Box>

              {/* What's Included */}
              <Paper elevation={1} sx={{ p: 2, backgroundColor: '#F8F9FA', borderRadius: '12px' }}>
                <Typography variant="subtitle2" gutterBottom fontWeight={600}>
                  What's Included:
                </Typography>
                <List dense sx={{ p: 0 }}>
                  {registrationServices.benefits.slice(0, 4).map((benefit, index) => (
                    <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                      <CheckCircleIcon sx={{ fontSize: 16, color: 'success.main', mr: 1 }} />
                      <ListItemText
                        primary={benefit.title}
                        primaryTypographyProps={{ fontSize: '0.85rem' }}
                      />
                    </ListItem>
                  ))}
                  <ListItem sx={{ px: 0, py: 0.5 }}>
                    <Typography variant="caption" color="text.secondary">
                      + 4 more benefits included
                    </Typography>
                  </ListItem>
                </List>
              </Paper>
            </CardContent>
          </Card>
        </Grid>

        {/* Registration Action */}
        <Grid item xs={12} md={7}>
          <Card elevation={3} sx={{ borderRadius: '16px' }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h5" gutterBottom fontWeight={600}>
                Ready to Register?
              </Typography>

              <Typography variant="body1" color="text.secondary" paragraph>
                Click the button below to proceed to our comprehensive farmer registration form where you can provide your farming details and complete your registration.
              </Typography>

              {error && (
                <Alert severity="error" sx={{ mb: 3, borderRadius: '8px' }}>
                  {error}
                </Alert>
              )}

              {/* Registration Process Steps */}
              <Paper
                elevation={1}
                sx={{
                  p: 3,
                  mb: 3,
                  backgroundColor: '#F8F9FA',
                  borderRadius: '12px'
                }}
              >
                <Typography variant="h6" gutterBottom fontWeight={600}>
                  Registration Process:
                </Typography>
                <List dense>
                  <ListItem sx={{ px: 0 }}>
                    <CheckCircleIcon sx={{ fontSize: 20, color: 'success.main', mr: 2 }} />
                    <ListItemText primary="Fill out the detailed registration form" />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <CheckCircleIcon sx={{ fontSize: 20, color: 'success.main', mr: 2 }} />
                    <ListItemText primary="Pay the one-time registration fee of ₹4,725" />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <CheckCircleIcon sx={{ fontSize: 20, color: 'success.main', mr: 2 }} />
                    <ListItemText primary="Receive confirmation and membership details" />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <CheckCircleIcon sx={{ fontSize: 20, color: 'success.main', mr: 2 }} />
                    <ListItemText primary="Get contacted by our expert within 24 hours" />
                  </ListItem>
                </List>
              </Paper>

              {/* Security Notice */}
              <Paper
                elevation={1}
                sx={{
                  p: 2,
                  mb: 3,
                  backgroundColor: '#E8F5E8',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                <SecurityIcon sx={{ color: 'success.main', mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  Your information is secure and will be used only for registration purposes
                </Typography>
              </Paper>

              {/* Registration Button */}
              <Button
                fullWidth
                variant="contained"
                size="large"
                onClick={handleRegistration}
                disabled={loading}
                sx={{
                  py: 2,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  borderRadius: '12px',
                  background: 'linear-gradient(45deg, #1B4C35 30%, #2E7D32 90%)',
                  '&:hover': {
                    background: 'linear-gradient(45deg, #0A3622 30%, #1B4C35 90%)',
                  }
                }}
              >
                {loading ? (
                  <>
                    <CircularProgress size={24} sx={{ mr: 2, color: 'white' }} />
                    Redirecting...
                  </>
                ) : (
                  'Proceed to Farmer Registration Form'
                )}
              </Button>

              <Typography variant="caption" display="block" textAlign="center" sx={{ mt: 2 }} color="text.secondary">
                By proceeding, you agree to our Terms of Service and Privacy Policy
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Checkout;
