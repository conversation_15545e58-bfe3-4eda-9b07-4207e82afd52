/**
 * Weather API Proxy Function
 * This Netlify function proxies requests to the WeatherAPI.com service
 * to avoid Content Security Policy restrictions in the browser
 */

const axios = require('axios');

exports.handler = async function(event, context) {
  // Only allow GET requests
  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      body: JSON.stringify({ error: 'Method Not Allowed' }),
      headers: { 'Content-Type': 'application/json' }
    };
  }

  try {
    // Get API key from environment or use the provided one
    const apiKey = process.env.WEATHER_API_KEY || 'cb49c5efa79f49c2967162757252707';
    
    // Get location from query parameter or default to Bangalore
    const location = event.queryStringParameters?.location || 'Bangalore';
    
    // Make request to WeatherAPI
    const response = await axios.get(
      `https://api.weatherapi.com/v1/current.json?key=${apiKey}&q=${location}&aqi=no`
    );
    
    // Return the data
    return {
      statusCode: 200,
      body: JSON.stringify(response.data),
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300' // Cache for 5 minutes
      }
    };
  } catch (error) {
    console.error('Weather API error:', error);
    
    return {
      statusCode: error.response?.status || 500,
      body: JSON.stringify({ 
        error: 'Error fetching weather data',
        message: error.message
      }),
      headers: { 'Content-Type': 'application/json' }
    };
  }
};