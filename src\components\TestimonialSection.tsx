import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Avatar,
  Rating,
  useTheme,
  
  Container,
} from '@mui/material';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
// import { useLanguage } from '../contexts/LanguageContext'; // No longer needed as we removed multilingual support for testimonials
import testimonialContent from '../content/home/<USER>';
import AnimatedText from './AnimatedText';

interface TestimonialProps {
  paddingTop?: number;
  paddingBottom?: number;
  backgroundColor?: string;
}

const TestimonialSection: React.FC<TestimonialProps> = ({
  paddingTop = 8,
  paddingBottom = 8,
  backgroundColor = 'rgba(76, 175, 80, 0.05)',
}) => {
  const theme = useTheme();
  // Language context is no longer needed as we removed multilingual support for testimonials
  // const { language } = useLanguage();

  // Settings for the slider
  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: window.innerWidth < 600 ? 1 : window.innerWidth < 960 ? 2 : 3,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 5000,
    pauseOnHover: true,
    responsive: [
      {
        breakpoint: 960,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  // Get the section title and subtitle
  const sectionTitle = testimonialContent.sectionTitle;
  const sectionSubtitle = testimonialContent.sectionSubtitle;

  return (
    <Box
      sx={{
        py: { paddingTop, paddingBottom },
        backgroundColor,
        overflow: 'hidden',
      }}
    >
      <Container maxWidth="lg">
        {/* Section Title */}
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <AnimatedText
            text={sectionTitle}
            animation="slideUp"
            delay={0.2}
            duration={0.8}
            variant="h3"
            sx={{
              fontWeight: 700,
              color: theme.palette.primary.main,
              mb: 2,
            }}
          />
          <Typography
            variant="h6"
            color="textSecondary"
            sx={{
              fontWeight: 400,
              maxWidth: '800px',
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            {sectionSubtitle}
          </Typography>
        </Box>

        {/* Testimonials Slider */}
        <Slider {...settings}>
          {testimonialContent.testimonials.map((testimonial, index) => {
            // Get the testimonial content
            const name = testimonial.name;
            const role = testimonial.role;
            const content = testimonial.testimonial;

            return (
              <Box key={index} sx={{ px: 2, py: 1 }}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    borderRadius: 4,
                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.05)',
                    transition: 'all 0.3s ease',
                    overflow: 'hidden',
                    position: 'relative',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 16px 32px rgba(0, 0, 0, 0.1)',
                    },
                  }}
                >
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '5px',
                      bgcolor: theme.palette.primary.main,
                    }}
                  />
                  <CardContent sx={{ p: 4, flexGrow: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <Avatar
                        src={testimonial.image}
                        alt={name}
                        sx={{
                          width: 64,
                          height: 64,
                          border: `2px solid ${theme.palette.primary.main}`,
                        }}
                      />
                      <Box sx={{ ml: 2 }}>
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          {name}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {role}
                        </Typography>
                        <Rating
                          value={testimonial.rating}
                          readOnly
                          size="small"
                          sx={{ mt: 0.5 }}
                        />
                      </Box>
                    </Box>
                    <Typography
                      variant="body1"
                      color="textSecondary"
                      sx={{
                        fontStyle: 'italic',
                        lineHeight: 1.6,
                        position: 'relative',
                        '&:before': {
                          content: '"\u201C"',
                          fontSize: '3rem',
                          color: 'rgba(76, 175, 80, 0.2)',
                          position: 'absolute',
                          top: -20,
                          left: -10,
                        },
                        '&:after': {
                          content: '"\u201D"',
                          fontSize: '3rem',
                          color: 'rgba(76, 175, 80, 0.2)',
                          position: 'absolute',
                          bottom: -50,
                          right: -10,
                        },
                      }}
                    >
                      {content}
                    </Typography>
                  </CardContent>
                </Card>
              </Box>
            );
          })}
        </Slider>
      </Container>
    </Box>
  );
};

export default TestimonialSection;