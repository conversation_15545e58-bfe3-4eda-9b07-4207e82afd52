import React, { useRef, useEffect, useState } from 'react';
import { Box, Container, Typography, Button, useTheme, useMediaQuery } from '@mui/material';
import { styled } from '@mui/material/styles';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import { Link as RouterLink } from 'react-router-dom';

interface SectionContainerProps {
  title: React.ReactNode;
  subtitle?: string;
  bgColor?: string;
  textColor?: string;
  linkTo?: string;
  linkText?: string;
  fullWidth?: boolean;
  children: React.ReactNode;
  marginTop?: number | string;
  marginBottom?: number | string;
  paddingTop?: number | string;
  paddingBottom?: number | string;
}

const SectionHeader = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(6),
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-16px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '80px',
    height: '4px',
    backgroundColor: theme.palette.primary.main,
    borderRadius: '2px',
  },
}));

const SectionContainer: React.FC<SectionContainerProps> = ({
  title,
  subtitle,
  bgColor,
  textColor,
  linkTo,
  linkText = 'View More',
  fullWidth = false,
  children,
  marginTop,
  marginBottom,
  paddingTop,
  paddingBottom,
}) => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.unobserve(entry.target);
        }
      },
      {
        root: null,
        rootMargin: '0px',
        threshold: 0.1,
      }
    );

    const currentRef = sectionRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, []);

  return (
    <Box
      ref={sectionRef}
      sx={{
        backgroundColor: bgColor || 'transparent',
        color: textColor || 'inherit',
        position: 'relative',
        mt: marginTop || { xs: theme.spacing(2), sm: theme.spacing(3), md: theme.spacing(4) },
        mb: marginBottom || { xs: theme.spacing(2), sm: theme.spacing(3), md: theme.spacing(4) },
        mx: { xs: theme.spacing(1), sm: theme.spacing(2), md: theme.spacing(3) },
        pt: paddingTop || { xs: theme.spacing(4), sm: theme.spacing(6), md: theme.spacing(8) },
        pb: paddingBottom || { xs: theme.spacing(4), sm: theme.spacing(6), md: theme.spacing(8) },
        overflow: 'hidden',
        borderRadius: { xs: '16px', sm: '20px', md: '24px' },
        boxShadow: bgColor ? '0 4px 20px rgba(0, 0, 0, 0.05)' : 'none',
        transition: 'opacity 0.5s ease-in-out, transform 0.5s ease-in-out',
        opacity: isVisible ? 1 : 0,
        transform: isVisible ? 'translateY(0)' : 'translateY(40px)',
      }}
    >
      <Container
        maxWidth={fullWidth ? false : 'lg'}
        disableGutters={fullWidth}
        sx={{
          px: { xs: 2, sm: 3, md: 4, lg: 5 },
          width: '100%',
          boxSizing: 'border-box'
        }}
      >
        <SectionHeader>
          {typeof title === 'string' ? (
            <Typography
              variant={isMobile ? 'h4' : 'h3'}
              component="h2"
              align="center"
              gutterBottom
              sx={{
                fontWeight: 700,
                color: textColor || 'inherit',
              }}
            >
              {title}
            </Typography>
          ) : (
            <Box sx={{ textAlign: 'center', mb: 2 }}>
              {title}
            </Box>
          )}
          {subtitle && (
            <Typography
              variant="subtitle1"
              align="center"
              color={textColor ? textColor : 'text.secondary'}
              sx={{ maxWidth: '800px', mx: 'auto', px: { xs: 2, md: 0 } }}
            >
              {subtitle}
            </Typography>
          )}
        </SectionHeader>

        {children}

        {linkTo && (
          <Box sx={{ textAlign: 'center', mt: 4 }}>
            <Button
              component={RouterLink}
              to={linkTo}
              variant="outlined"
              color="primary"
              endIcon={<ArrowForwardIcon />}
              sx={{
                borderRadius: '50px',
                px: 3,
                py: 1,
                borderWidth: 2,
                '&:hover': {
                  borderWidth: 2,
                },
              }}
            >
              {linkText}
            </Button>
          </Box>
        )}
      </Container>
    </Box>
  );
};

export default SectionContainer;
