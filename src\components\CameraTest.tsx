import React, { useState, useRef, useEffect } from 'react';
import {
  <PERSON>,
  Button,
  Typo<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Camera as CameraIcon,
  Stop as StopIcon
} from '@mui/icons-material';

const CameraTest: React.FC = () => {
  const [cameraOpen, setCameraOpen] = useState(false);
  const [isVideoReady, setIsVideoReady] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [cameraInfo, setCameraInfo] = useState<any>(null);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (videoRef.current && videoRef.current.srcObject) {
        const stream = videoRef.current.srcObject as MediaStream;
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  const testCameraAccess = async () => {
    setError(null);
    setSuccess(null);
    setCameraInfo(null);
    
    // Check basic support
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      setError('❌ Camera API not supported in this browser');
      return;
    }

    // Check secure context
    if (window.location.protocol !== 'https:' && 
        window.location.hostname !== 'localhost' && 
        window.location.hostname !== '127.0.0.1') {
      setError('❌ Camera requires HTTPS or localhost');
      return;
    }

    try {
      // Get available devices
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');
      
      if (videoDevices.length === 0) {
        setError('❌ No camera devices found');
        return;
      }

      setCameraInfo({
        totalDevices: devices.length,
        videoDevices: videoDevices.length,
        devices: videoDevices.map(d => ({ label: d.label || 'Unknown Camera', id: d.deviceId }))
      });

      // Test camera access
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: { ideal: 'environment' },
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setCameraOpen(true);
        setIsVideoReady(false);
        
        videoRef.current.onloadedmetadata = () => {
          setIsVideoReady(true);
          setSuccess('✅ Camera access successful!');
        };
        
        await videoRef.current.play();
      }

    } catch (err: any) {
      let errorMessage = '❌ Camera access failed: ';
      
      switch (err.name) {
        case 'NotAllowedError':
          errorMessage += 'Permission denied. Please allow camera access.';
          break;
        case 'NotFoundError':
          errorMessage += 'No camera found.';
          break;
        case 'NotReadableError':
          errorMessage += 'Camera is in use by another application.';
          break;
        case 'OverconstrainedError':
          errorMessage += 'Camera constraints not supported.';
          break;
        case 'SecurityError':
          errorMessage += 'Security error. Check HTTPS/permissions.';
          break;
        default:
          errorMessage += err.message || 'Unknown error';
      }
      
      setError(errorMessage);
      console.error('Camera error:', err);
    }
  };

  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    setCameraOpen(false);
    setIsVideoReady(false);
    setSuccess(null);
  };

  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current || !isVideoReady) {
      setError('❌ Camera not ready for capture');
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) {
      setError('❌ Canvas not supported');
      return;
    }

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    context.drawImage(video, 0, 0);

    canvas.toBlob((blob) => {
      if (blob) {
        setSuccess(`✅ Photo captured! Size: ${Math.round(blob.size / 1024)}KB`);
      } else {
        setError('❌ Failed to capture photo');
      }
    }, 'image/jpeg', 0.9);
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Camera Test Utility
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3 }}>
        This utility tests camera functionality to help diagnose issues.
      </Typography>

      {/* Test Results */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      {/* Camera Info */}
      {cameraInfo && (
        <Card sx={{ mb: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Camera Information
            </Typography>
            <Typography variant="body2">
              Total devices: {cameraInfo.totalDevices}
            </Typography>
            <Typography variant="body2">
              Video devices: {cameraInfo.videoDevices}
            </Typography>
            {cameraInfo.devices.map((device: any, index: number) => (
              <Typography key={index} variant="body2">
                📹 {device.label}
              </Typography>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Controls */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <Button
          variant="contained"
          startIcon={<CameraIcon />}
          onClick={testCameraAccess}
          disabled={cameraOpen}
        >
          Test Camera Access
        </Button>
        
        {cameraOpen && (
          <Button
            variant="outlined"
            startIcon={<StopIcon />}
            onClick={stopCamera}
            color="error"
          >
            Stop Camera
          </Button>
        )}
      </Box>

      {/* Camera Dialog */}
      <Dialog open={cameraOpen} onClose={stopCamera} maxWidth="md" fullWidth>
        <DialogTitle>Camera Test</DialogTitle>
        <DialogContent>
          <Box sx={{ 
            position: 'relative', 
            bgcolor: 'black', 
            minHeight: 300,
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center',
            borderRadius: 1
          }}>
            {!isVideoReady && (
              <Box sx={{ 
                position: 'absolute', 
                display: 'flex', 
                flexDirection: 'column', 
                alignItems: 'center', 
                color: 'white',
                zIndex: 2
              }}>
                <CircularProgress color="inherit" sx={{ mb: 2 }} />
                <Typography variant="body2">Loading camera...</Typography>
              </Box>
            )}
            
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              style={{ 
                width: '100%', 
                height: 'auto',
                maxHeight: 400,
                display: isVideoReady ? 'block' : 'none'
              }}
            />
            
            <canvas 
              ref={canvasRef} 
              style={{ display: 'none' }} 
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={stopCamera}>Close</Button>
          <Button 
            onClick={capturePhoto} 
            variant="contained" 
            disabled={!isVideoReady}
          >
            Test Capture
          </Button>
        </DialogActions>
      </Dialog>

      {/* Debug Info */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Debug Information
          </Typography>
          <Typography variant="body2">
            🌐 Protocol: {window.location.protocol}
          </Typography>
          <Typography variant="body2">
            🏠 Hostname: {window.location.hostname}
          </Typography>
          <Typography variant="body2">
            📱 User Agent: {navigator.userAgent.substring(0, 100)}...
          </Typography>
          <Typography variant="body2">
            🎥 MediaDevices API: {navigator.mediaDevices ? '✅ Available' : '❌ Not Available'}
          </Typography>
          <Typography variant="body2">
            🔒 Secure Context: {window.isSecureContext ? '✅ Yes' : '❌ No'}
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default CameraTest;
