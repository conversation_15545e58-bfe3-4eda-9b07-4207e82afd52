import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Grid,
  TextField,
  Button,
  FormControlLabel,
  Checkbox,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText
} from '@mui/material';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import { useSearchParams, useLocation } from 'react-router-dom';
import config from '../config';
import { PayUModal } from '../components/Payment';
import PaymentFailureDialog from '../components/Payment/PaymentFailureDialog';

const validationSchema = Yup.object({
  name: Yup.string().required('Name is required'),
  mobile: Yup.string()
    .matches(/^[6-9]\d{9}$/, 'Please enter a valid 10-digit mobile number')
    .required('Mobile number is required'),
  email: Yup.string().email('Invalid email').required('Email is required'),
  address: Yup.string().required('Address is required'),
  city: Yup.string().required('City is required'),
  district: Yup.string().required('District is required'),
  taluk: Yup.string().required('Taluk is required'),
  landArea: Yup.string().required('Land area is required'),
  soilType: Yup.string().required('Soil type is required'),
  termsAccepted: Yup.boolean().oneOf([true], 'You must accept the terms and conditions')
});

const FormPage = () => {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [formData, setFormData] = useState<any>(null);
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [paymentFailureOpen, setPaymentFailureOpen] = useState(false);
  const [paymentError, setPaymentError] = useState<string>('');

  const [searchParams] = useSearchParams();
  const location = useLocation();

  const t = {
    title: 'Farmer Registration Form',
    name: 'Full Name',
    mobile: 'Mobile Number',
    email: 'Email Address',
    address: 'Address',
    city: 'City',
    district: 'District',
    taluk: 'Taluk',
    landArea: 'Land Area (in acres)',
    soilType: 'Soil Type',
    irrigationFacilities: 'Irrigation Facilities Available',
    termsAccepted: 'I accept the terms and conditions',
    previewProceed: 'Preview & Proceed',
    previewTitle: 'Review Your Information',
    cancel: 'Cancel'
  };

  const handlePaymentFailureFromUrl = React.useCallback((storedData: string | null) => {
    if (storedData) {
      try {
        const parsedData = JSON.parse(storedData);
        setFormData(parsedData);
      } catch (error) {
        // Error parsing stored data - continue with empty form
      }
    }
    const error = searchParams.get('error') || 'Payment failed';
    setPaymentError(error);
    setPaymentFailureOpen(true);
  }, [searchParams]);

  useEffect(() => {
    const paymentStatus = searchParams.get('payment');
    if (paymentStatus === 'failure') {
      const storedData = localStorage.getItem('payuCheckoutData');
      handlePaymentFailureFromUrl(storedData);
      setTimeout(() => {
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
      }, 1000);
    }
  }, [searchParams, handlePaymentFailureFromUrl]);

  useEffect(() => {
    if (location.state?.fromSuccess) {
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  const handlePreview = (values: any) => {
    setFormData(values);
    setPreviewOpen(true);
  };

  const handleClosePreview = () => {
    setPreviewOpen(false);
  };

  const handleProceedToRegistration = () => {
    setPreviewOpen(false);
    setPaymentModalOpen(true);
  };



  const handlePaymentFailureClose = () => {
    setPaymentFailureOpen(false);
    setPaymentError('');
  };

  const handlePaymentFailureRetry = () => {
    setPaymentFailureOpen(false);
    setPaymentModalOpen(true);
  };

  const handlePaymentSuccess = () => {
    setPaymentModalOpen(false);
  };

  const handlePaymentFailure = (error: string) => {
    setPaymentModalOpen(false);
    setPaymentError(error);
    setPaymentFailureOpen(true);
  };

  const PreviewDialog = () => (
    <Dialog
      open={previewOpen}
      onClose={handleClosePreview}
      maxWidth="sm"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          margin: { xs: 1, sm: 2 },
          maxHeight: { xs: '95vh', sm: '90vh' },
          borderRadius: { xs: '12px', sm: '16px' }
        }
      }}
    >
      <DialogTitle sx={{
        fontSize: { xs: '1.25rem', sm: '1.5rem' },
        fontWeight: 600,
        pb: { xs: 1, sm: 2 }
      }}>
        {t.previewTitle}
      </DialogTitle>
      <DialogContent sx={{ px: { xs: 2, sm: 3 } }}>
        <List sx={{ py: 0 }}>
          <ListItem sx={{ px: 0, py: 1 }}>
            <ListItemText
              primary={t.name}
              secondary={formData?.name}
              primaryTypographyProps={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
                fontWeight: 500
              }}
              secondaryTypographyProps={{
                fontSize: { xs: '1rem', sm: '1.125rem' },
                color: 'text.primary',
                fontWeight: 400
              }}
            />
          </ListItem>
          <ListItem sx={{ px: 0, py: 1 }}>
            <ListItemText
              primary={t.mobile}
              secondary={formData?.mobile}
              primaryTypographyProps={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
                fontWeight: 500
              }}
              secondaryTypographyProps={{
                fontSize: { xs: '1rem', sm: '1.125rem' },
                color: 'text.primary',
                fontWeight: 400
              }}
            />
          </ListItem>
          <ListItem sx={{ px: 0, py: 1 }}>
            <ListItemText
              primary={t.email}
              secondary={formData?.email}
              primaryTypographyProps={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
                fontWeight: 500
              }}
              secondaryTypographyProps={{
                fontSize: { xs: '1rem', sm: '1.125rem' },
                color: 'text.primary',
                fontWeight: 400
              }}
            />
          </ListItem>
          <ListItem sx={{ px: 0, py: 1 }}>
            <ListItemText
              primary={t.address}
              secondary={formData?.address}
              primaryTypographyProps={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
                fontWeight: 500
              }}
              secondaryTypographyProps={{
                fontSize: { xs: '1rem', sm: '1.125rem' },
                color: 'text.primary',
                fontWeight: 400
              }}
            />
          </ListItem>
          <ListItem sx={{ px: 0, py: 1 }}>
            <ListItemText
              primary={t.city}
              secondary={formData?.city}
              primaryTypographyProps={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
                fontWeight: 500
              }}
              secondaryTypographyProps={{
                fontSize: { xs: '1rem', sm: '1.125rem' },
                color: 'text.primary',
                fontWeight: 400
              }}
            />
          </ListItem>
          <ListItem sx={{ px: 0, py: 1 }}>
            <ListItemText
              primary="State"
              secondary="Karnataka"
              primaryTypographyProps={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
                fontWeight: 500
              }}
              secondaryTypographyProps={{
                fontSize: { xs: '1rem', sm: '1.125rem' },
                color: 'text.primary',
                fontWeight: 400
              }}
            />
          </ListItem>
          <ListItem sx={{ px: 0, py: 1 }}>
            <ListItemText
              primary={t.district}
              secondary={formData?.district}
              primaryTypographyProps={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
                fontWeight: 500
              }}
              secondaryTypographyProps={{
                fontSize: { xs: '1rem', sm: '1.125rem' },
                color: 'text.primary',
                fontWeight: 400
              }}
            />
          </ListItem>
          <ListItem sx={{ px: 0, py: 1 }}>
            <ListItemText
              primary={t.taluk}
              secondary={formData?.taluk}
              primaryTypographyProps={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
                fontWeight: 500
              }}
              secondaryTypographyProps={{
                fontSize: { xs: '1rem', sm: '1.125rem' },
                color: 'text.primary',
                fontWeight: 400
              }}
            />
          </ListItem>
          <ListItem sx={{ px: 0, py: 1 }}>
            <ListItemText
              primary={t.landArea}
              secondary={formData?.landArea}
              primaryTypographyProps={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
                fontWeight: 500
              }}
              secondaryTypographyProps={{
                fontSize: { xs: '1rem', sm: '1.125rem' },
                color: 'text.primary',
                fontWeight: 400
              }}
            />
          </ListItem>
          <ListItem sx={{ px: 0, py: 1 }}>
            <ListItemText
              primary={t.soilType}
              secondary={formData?.soilType}
              primaryTypographyProps={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
                fontWeight: 500
              }}
              secondaryTypographyProps={{
                fontSize: { xs: '1rem', sm: '1.125rem' },
                color: 'text.primary',
                fontWeight: 400
              }}
            />
          </ListItem>
          <ListItem sx={{ px: 0, py: 1 }}>
            <ListItemText
              primary={t.irrigationFacilities}
              secondary={formData?.irrigationFacilities ? 'Yes' : 'No'}
              primaryTypographyProps={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
                fontWeight: 500
              }}
              secondaryTypographyProps={{
                fontSize: { xs: '1rem', sm: '1.125rem' },
                color: 'text.primary',
                fontWeight: 400
              }}
            />
          </ListItem>
        </List>
      </DialogContent>
      <DialogActions sx={{
        p: { xs: 2, sm: 3 },
        gap: { xs: 1, sm: 2 },
        flexDirection: { xs: 'column', sm: 'row' }
      }}>
        <Button
          onClick={handleClosePreview}
          sx={{
            minHeight: { xs: '48px', sm: '44px' },
            fontSize: { xs: '1rem', sm: '0.875rem' },
            order: { xs: 2, sm: 1 },
            width: { xs: '100%', sm: 'auto' }
          }}
        >
          {t.cancel}
        </Button>
        <Button
          onClick={handleProceedToRegistration}
          variant="contained"
          color="primary"
          sx={{
            minHeight: { xs: '48px', sm: '44px' },
            fontSize: { xs: '1rem', sm: '0.875rem' },
            fontWeight: 600,
            order: { xs: 1, sm: 2 },
            width: { xs: '100%', sm: 'auto' }
          }}
        >
          {`Pay ${config.payu.paymentAmountDisplay} & Register`}
        </Button>
      </DialogActions>
    </Dialog>
  );

  return (
    <Container maxWidth="md" sx={{
      py: { xs: 2, sm: 3, md: 4 },
      px: { xs: 1, sm: 2, md: 3 }
    }}>
      <Paper elevation={3} sx={{
        p: { xs: 2, sm: 3, md: 4 },
        borderRadius: { xs: '12px', sm: '16px' },
        mx: { xs: 0, sm: 'auto' }
      }}>
        <Typography
          variant="h4"
          gutterBottom
          align="center"
          color="primary"
          sx={{
            fontSize: { xs: '1.75rem', sm: '2rem', md: '2.125rem' },
            mb: { xs: 2, sm: 3, md: 4 }
          }}
        >
          {t.title}
        </Typography>
        
        <Formik
          initialValues={{
            name: '',
            mobile: '',
            email: '',
            address: '',
            city: '',
            state: 'Karnataka',
            district: '',
            taluk: '',
            landArea: '',
            soilType: '',
            irrigationFacilities: false,
            termsAccepted: false
          }}
          validationSchema={validationSchema}
          onSubmit={handlePreview}
        >
          {({ errors, touched }) => (
            <Form>
              <Grid container spacing={{ xs: 2, sm: 3 }}>
                <Grid item xs={12} sm={6}>
                  <Field name="name" label={t.name} as={TextField} fullWidth
                    error={touched.name && Boolean(errors.name)}
                    helperText={touched.name && errors.name}
                    sx={{
                      '& .MuiInputBase-root': {
                        minHeight: { xs: '56px', sm: '56px' }
                      }
                    }} />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Field name="mobile" label={t.mobile} as={TextField} fullWidth
                    error={touched.mobile && Boolean(errors.mobile)}
                    helperText={touched.mobile && errors.mobile}
                    inputProps={{
                      inputMode: 'numeric',
                      pattern: '[0-9]*'
                    }}
                    sx={{
                      '& .MuiInputBase-root': {
                        minHeight: { xs: '56px', sm: '56px' }
                      }
                    }} />
                </Grid>
                <Grid item xs={12}>
                  <Field name="email" label={t.email} as={TextField} fullWidth
                    error={touched.email && Boolean(errors.email)}
                    helperText={touched.email && errors.email}
                    inputProps={{
                      inputMode: 'email'
                    }}
                    sx={{
                      '& .MuiInputBase-root': {
                        minHeight: { xs: '56px', sm: '56px' }
                      }
                    }} />
                </Grid>
                <Grid item xs={12}>
                  <Field name="address">
                    {({ field }: any) => (
                      <TextField
                        {...field}
                        label={t.address}
                        fullWidth
                        multiline
                        rows={{ xs: 3, sm: 3 }}
                        error={touched.address && Boolean(errors.address)}
                        helperText={touched.address && errors.address}
                        sx={{
                          '& .MuiInputBase-root': {
                            minHeight: { xs: '80px', sm: '80px' }
                          }
                        }}
                      />
                    )}
                  </Field>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Field name="city" label={t.city} as={TextField} fullWidth
                    error={touched.city && Boolean(errors.city)}
                    helperText={touched.city && errors.city}
                    sx={{
                      '& .MuiInputBase-root': {
                        minHeight: { xs: '56px', sm: '56px' }
                      }
                    }} />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    label="State"
                    value="Karnataka"
                    fullWidth
                    disabled
                    sx={{
                      '& .MuiInputBase-input': {
                        color: 'text.primary',
                        fontWeight: 500
                      },
                      '& .MuiInputLabel-root': {
                        color: 'text.secondary'
                      },
                      '& .MuiInputBase-root': {
                        minHeight: { xs: '56px', sm: '56px' }
                      }
                    }}
                    helperText="Service available only in Karnataka"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Field name="district" label={t.district} as={TextField} fullWidth
                    error={touched.district && Boolean(errors.district)}
                    helperText={touched.district && errors.district}
                    sx={{
                      '& .MuiInputBase-root': {
                        minHeight: { xs: '56px', sm: '56px' }
                      }
                    }} />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Field name="taluk" label={t.taluk} as={TextField} fullWidth
                    error={touched.taluk && Boolean(errors.taluk)}
                    helperText={touched.taluk && errors.taluk}
                    sx={{
                      '& .MuiInputBase-root': {
                        minHeight: { xs: '56px', sm: '56px' }
                      }
                    }} />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Field name="landArea" label={t.landArea} as={TextField} fullWidth
                    error={touched.landArea && Boolean(errors.landArea)}
                    helperText={touched.landArea && errors.landArea}
                    inputProps={{
                      inputMode: 'decimal'
                    }}
                    sx={{
                      '& .MuiInputBase-root': {
                        minHeight: { xs: '56px', sm: '56px' }
                      }
                    }} />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth error={touched.soilType && Boolean(errors.soilType)}
                    sx={{
                      '& .MuiInputBase-root': {
                        minHeight: { xs: '56px', sm: '56px' }
                      }
                    }}>
                    <InputLabel>{t.soilType}</InputLabel>
                    <Field name="soilType">
                      {({ field }: any) => (
                        <Select {...field} label={t.soilType}
                          MenuProps={{
                            PaperProps: {
                              sx: {
                                maxHeight: { xs: '50vh', sm: '40vh' }
                              }
                            }
                          }}>
                          <MenuItem value="Red Soil">Red Soil</MenuItem>
                          <MenuItem value="Black Soil">Black Soil</MenuItem>
                          <MenuItem value="Alluvial Soil">Alluvial Soil</MenuItem>
                          <MenuItem value="Laterite Soil">Laterite Soil</MenuItem>
                          <MenuItem value="Sandy Soil">Sandy Soil</MenuItem>
                        </Select>
                      )}
                    </Field>
                    {touched.soilType && errors.soilType && <FormHelperText>{errors.soilType}</FormHelperText>}
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <Field name="irrigationFacilities">
                    {({ field }: any) => (
                      <FormControlLabel
                        control={
                          <Checkbox
                            {...field}
                            checked={field.value}
                            sx={{
                              '& .MuiSvgIcon-root': {
                                fontSize: { xs: '1.5rem', sm: '1.25rem' }
                              }
                            }}
                          />
                        }
                        label={t.irrigationFacilities}
                        sx={{
                          '& .MuiFormControlLabel-label': {
                            fontSize: { xs: '1rem', sm: '0.875rem' },
                            lineHeight: 1.5
                          },
                          alignItems: 'flex-start',
                          mt: 1
                        }} />
                    )}
                  </Field>
                </Grid>
                <Grid item xs={12}>
                  <Field name="termsAccepted">
                    {({ field }: any) => (
                      <FormControlLabel
                        control={
                          <Checkbox
                            {...field}
                            checked={field.value}
                            sx={{
                              '& .MuiSvgIcon-root': {
                                fontSize: { xs: '1.5rem', sm: '1.25rem' }
                              }
                            }}
                          />
                        }
                        label={t.termsAccepted}
                        sx={{
                          '& .MuiFormControlLabel-label': {
                            fontSize: { xs: '1rem', sm: '0.875rem' },
                            lineHeight: 1.5
                          },
                          alignItems: 'flex-start',
                          mt: 1
                        }} />
                    )}
                  </Field>
                  {touched.termsAccepted && errors.termsAccepted && (
                    <Typography color="error" variant="caption" display="block" sx={{ mt: 1 }}>
                      {errors.termsAccepted}
                    </Typography>
                  )}
                </Grid>
                <Grid item xs={12}>
                  <Box
                    display="flex"
                    justifyContent="center"
                    mt={{ xs: 3, sm: 2 }}
                    px={{ xs: 1, sm: 0 }}
                  >
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      size="large"
                      sx={{
                        width: { xs: '100%', sm: 'auto' },
                        minWidth: { xs: 'auto', sm: '200px' },
                        minHeight: { xs: '56px', sm: '48px' },
                        fontSize: { xs: '1.1rem', sm: '1rem' },
                        fontWeight: 600,
                        borderRadius: { xs: '12px', sm: '50px' },
                        maxWidth: { xs: '100%', sm: '300px' }
                      }}
                    >
                      {t.previewProceed}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </Form>
          )}
        </Formik>
      </Paper>

      <PreviewDialog />

      <PayUModal
        open={paymentModalOpen}
        onClose={() => setPaymentModalOpen(false)}
        formData={formData}
        onPaymentSuccess={handlePaymentSuccess}
        onPaymentFailure={handlePaymentFailure}
        customerName={formData?.name || ''}
        customerMobile={formData?.mobile || ''}
      />

      <PaymentFailureDialog
        open={paymentFailureOpen}
        onClose={handlePaymentFailureClose}
        onRetry={handlePaymentFailureRetry}
        error={paymentError}
      />
    </Container>
  );
};

export default FormPage;
