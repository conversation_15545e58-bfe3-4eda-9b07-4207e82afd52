# 🤖 AI Chatbot Setup Guide

Your Darvi Group website now includes an intelligent AI chatbot powered by Google Gemini! This guide will help you get it up and running.

## ✨ Features

- **Intelligent Responses**: Powered by Google Gemini AI with comprehensive knowledge about Darvi Group
- **Multi-language Support**: English, Kannada, and Hindi
- **Agricultural Expertise**: Specialized knowledge about farming, products, and services
- **Responsive Design**: Works perfectly on mobile and desktop
- **Fallback System**: Uses local vector search when API is unavailable
- **Context Awareness**: Remembers conversation history for better responses

## 🔧 Setup Instructions

### 1. Get Google Gemini API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated API key

### 2. Configure Environment Variables

**For Local Development:**
1. Create a `.env.local` file in your project root
2. Add your API key:
   ```
   REACT_APP_GEMINI_API_KEY=your_actual_api_key_here
   REACT_APP_GEMINI_API_URL=https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent
   ```

**For Netlify Production:**
1. Go to your Netlify dashboard
2. Navigate to Site Settings > Environment Variables
3. Add the following variables:
   - `REACT_APP_GEMINI_API_KEY`: Your actual Gemini API key
   - `REACT_APP_GEMINI_API_URL`: `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent`

### 3. Test the Chatbot

1. Deploy your site or run locally with `npm start`
2. Look for the floating chat button in the bottom-right corner
3. Click it to open the chat interface
4. Try asking questions like:
   - "What products do you offer?"
   - "Tell me about Genex"
   - "How can I improve my crop yield?"
   - "What services does Darvi Group provide?"

## 🎯 Chatbot Capabilities

The AI assistant can help with:

### 🌱 **Agricultural Guidance**
- Crop management advice
- Soil health recommendations
- Pest and disease management
- Irrigation solutions
- Sustainable farming practices

### 🛍️ **Product Information**
- Genex: Soil fertility enhancer
- Neem: Natural pest control
- Santica: Plant health improver
- Product pricing and availability

### 🏢 **Company Services**
- Agricultural consultation
- Site visits and assessments
- IoT-powered monitoring
- Farmer registration assistance
- Land management advice

### 📞 **Contact & Support**
- Contact information
- Business hours
- Location details
- Service areas

## 🔧 Customization Options

### Modify Welcome Messages
Edit the `getWelcomeMessage()` function in `src/components/ChatBot.tsx` to customize greetings.

### Add Quick Actions
Update the `getQuickActions()` function to add more suggested questions.

### Styling
The chatbot uses Material-UI components and follows your site's theme. Customize colors and styling in the ChatBot component.

### Content Updates
The AI has access to comprehensive data about Darvi Group through:
- `src/services/geminiService.ts` - Main AI service
- `src/data/darviContentData.ts` - Company information
- `src/services/simpleVectorService.ts` - Fallback search

## 🚨 Troubleshooting

### Chatbot Not Appearing
- Check that the ChatBot component is imported in `App.tsx`
- Verify environment variables are set correctly
- Check browser console for errors

### API Errors
- Verify your Gemini API key is correct and active
- Check API quota limits in Google AI Studio
- Ensure the API URL is correct

### Fallback Mode
If the Gemini API is unavailable, the chatbot automatically falls back to a local vector search system that can still provide helpful responses about Darvi Group.

## 💡 Best Practices

1. **Monitor Usage**: Keep track of API usage in Google AI Studio
2. **Update Content**: Regularly update the knowledge base in `darviContentData.ts`
3. **Test Regularly**: Ensure the chatbot provides accurate information
4. **User Feedback**: Monitor user interactions and improve responses

## 🔒 Security Notes

- API keys are only used in the frontend for this implementation
- Consider implementing a backend proxy for enhanced security in production
- The chatbot includes production-safe error handling
- No sensitive user data is stored in conversation history

## 📈 Analytics

The chatbot is ready for analytics integration. You can add tracking for:
- User engagement metrics
- Common questions
- Conversation completion rates
- User satisfaction

---

🎉 **Your AI chatbot is now ready to help visitors learn about Darvi Group's agricultural solutions!**

For technical support or customization requests, refer to the main project documentation or contact the development team.
