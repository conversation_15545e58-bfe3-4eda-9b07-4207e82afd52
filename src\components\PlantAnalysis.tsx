import React, { useState, useRef } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  IconButton,
  LinearProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  CameraAlt as CameraIcon,
  Close as CloseIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { useLanguage } from '../contexts/LanguageContext';
import geminiService from '../services/geminiService';
import translations from '../translations';

const PlantAnalysis = () => {
  const { language } = useLanguage();
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [cameraOpen, setCameraOpen] = useState(false);
  const [cameraError, setCameraError] = useState(false);
  // ✅ ADDED: State to track if the video stream is ready
  const [isVideoReady, setIsVideoReady] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Cleanup camera on component unmount
  useEffect(() => {
    return () => {
      if (videoRef.current && videoRef.current.srcObject) {
        const stream = videoRef.current.srcObject as MediaStream;
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  React.useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      validateAndProcessImage(file);
    }
  };

  const validateAndProcessImage = (file: File) => {
    setError('');
    const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      setError(translations[language as keyof typeof translations].plantAnalysis.invalidFileType);
      return;
    }
    if (file.size > 5 * 1024 * 1024) {
      setError(translations[language as keyof typeof translations].plantAnalysis.fileTooLarge);
      return;
    }
    setSelectedImage(file);
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
  };

  const openCamera = async () => {
    setIsVideoReady(false);
    setCameraError(false);
    setError('');

    // Check for camera support
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      setCameraError(true);
      setError('Camera access is not supported by your browser. Please use file upload instead.');
      return;
    }

    // Check for secure context (HTTPS or localhost)
    if (window.location.protocol !== 'https:' &&
        window.location.hostname !== 'localhost' &&
        window.location.hostname !== '127.0.0.1') {
      setCameraError(true);
      setError('Camera access requires a secure connection (HTTPS). Please use file upload or access via HTTPS.');
      return;
    }

    try {
      // First, open the camera dialog
      setCameraOpen(true);

      // Request camera permissions with better constraints
      const constraints = {
        video: {
          facingMode: { ideal: 'environment' }, // Prefer back camera but allow front
          width: { ideal: 1280, max: 1920 },
          height: { ideal: 720, max: 1080 }
        },
        audio: false
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);

      if (videoRef.current) {
        videoRef.current.srcObject = stream;

        // Add event listeners for better video handling
        videoRef.current.onloadedmetadata = () => {
          setIsVideoReady(true);
        };

        videoRef.current.oncanplay = () => {
          videoRef.current?.play().catch(() => {
            // Handle play error silently
          });
        };

        // Start playing the video
        await videoRef.current.play();
      }
    } catch (err: any) {
      setCameraError(true);

      let errorMessage = translations[language as keyof typeof translations].plantAnalysis.cameraError;

      if (err.name === 'NotAllowedError') {
        errorMessage = 'Camera permission denied. Please allow camera access and try again.';
      } else if (err.name === 'NotFoundError') {
        errorMessage = 'No camera found on this device. Please use file upload instead.';
      } else if (err.name === 'NotReadableError') {
        errorMessage = 'Camera is already in use by another application. Please close other apps and try again.';
      } else if (err.name === 'OverconstrainedError') {
        errorMessage = 'Camera constraints not supported. Trying with basic settings...';

        // Retry with basic constraints
        try {
          const basicStream = await navigator.mediaDevices.getUserMedia({ video: true });
          if (videoRef.current) {
            videoRef.current.srcObject = basicStream;
            setCameraError(false);
            return;
          }
        } catch (retryErr) {
          errorMessage = 'Unable to access camera. Please use file upload instead.';
        }
      }

      setError(errorMessage);
    }
  };

  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current) {
      setError('Camera not ready. Please wait and try again.');
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;

    // Check if video has valid dimensions
    if (video.videoWidth === 0 || video.videoHeight === 0) {
      setError('Camera feed not ready. Please wait and try again.');
      return;
    }

    try {
      const context = canvas.getContext('2d');
      if (!context) {
        setError('Unable to capture photo. Please try again.');
        return;
      }

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw the current video frame to canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert canvas to blob
      canvas.toBlob((blob) => {
        if (blob) {
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
          const file = new File([blob], `plant-photo-${timestamp}.jpg`, {
            type: 'image/jpeg',
            lastModified: Date.now()
          });



          validateAndProcessImage(file);
          closeCamera();
        } else {
          setError('Failed to capture photo. Please try again.');
        }
      }, 'image/jpeg', 0.9);

    } catch (err) {
      setError('Failed to capture photo. Please try again.');
    }
  };

  const closeCamera = () => {
    try {
      // Stop all media tracks
      if (videoRef.current && videoRef.current.srcObject) {
        const stream = videoRef.current.srcObject as MediaStream;
        const tracks = stream.getTracks();
        tracks.forEach(track => {
          track.stop();
        });

        // Clear the video source
        videoRef.current.srcObject = null;
      }
    } catch (err) {
      // Handle error silently
    } finally {
      // Reset states
      setCameraOpen(false);
      setIsVideoReady(false);
      setCameraError(false);
    }
  };

  const analyzePlant = async () => {
    if (!selectedImage) return;
    setIsAnalyzing(true);
    setError('');
    setAnalysisResult('');
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const base64Image = e.target?.result as string;
        const prompt = language === 'en'
          ? `Analyze this plant leaf image for diseases, pests, or nutrient deficiencies.`
          : language === 'kn'
          ? `ಈ ಸಸ್ಯದ ಎಲೆಯ ಚಿತ್ರವನ್ನು ರೋಗಗಳು, ಕೀಟಗಳು ಅಥವಾ ಪೌಷ್ಟಿಕಾಂಶ ಕೊರತೆಗಳಿಗಾಗಿ ವಿಶ್ಲೇಷಿಸಿ.`
          : `इस पौधे के पत्ते की छवि का विश्लेषण रोगों, कीटों या पोषक तत्वों की कमी के लिए करें।`;
        const result = await geminiService.analyzeImage(base64Image, prompt);
        setAnalysisResult(result);
      } catch (err) {
        setError(translations[language as keyof typeof translations].plantAnalysis.analysisError);
      } finally {
        setIsAnalyzing(false);
      }
    };
    reader.onerror = () => {
      setError('Failed to read the image file.');
      setIsAnalyzing(false);
    };
    reader.readAsDataURL(selectedImage);
  };

  const resetAnalysis = () => {
    setSelectedImage(null);
    setPreviewUrl(null);
    setAnalysisResult('');
    setError('');
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
  };

  const t = translations[language as keyof typeof translations].plantAnalysis;

  const renderResult = (text: string) => {
    const formattedText = text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\n/g, '<br />');
    return { __html: formattedText };
  };

  return (
    <Box sx={{ maxWidth: '1200px', mx: 'auto', p: { xs: 2, sm: 3 } }}>
      <Grid container spacing={4}>
        {/* Left Column */}
        <Grid item xs={12} md={5}>
            <Card sx={{ height: '100%', bgcolor: 'rgba(255, 255, 255, 0.9)', backdropFilter: 'blur(10px)' }}>
                <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    <Typography variant="h5" gutterBottom sx={{ color: '#1B4C35', fontWeight: 600 }}>{t.title}</Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>{t.description}</Typography>
                    <Box sx={{ mb: 3 }}>
                        <Typography variant="h6" sx={{ color: '#2E7D32', fontWeight: 600, mb: 2 }}>{t.infoTitle}</Typography>
                        <Grid container spacing={1}>
                            {[t.infoPoint1, t.infoPoint2, t.infoPoint3, t.infoPoint4].map((point, index) => (
                                <Grid item xs={12} key={index}>
                                    <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                                        <CheckCircleIcon sx={{ color: '#4caf50', mr: 1.5, mt: 0.25, flexShrink: 0 }} />
                                        <Typography variant="body2" color="text.secondary">{point}</Typography>
                                    </Box>
                                </Grid>
                            ))}
                        </Grid>
                    </Box>
                    <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                        <Button variant="outlined" startIcon={<UploadIcon />} component="label" sx={{ borderColor: '#4caf50', color: '#4caf50', fontWeight: 600, textTransform: 'none', '&:hover': { borderColor: '#43a047', bgcolor: 'rgba(76, 175, 80, 0.05)' } }}>
                            {t.browseFiles}
                            <input type="file" hidden accept="image/*" onChange={handleImageChange} />
                        </Button>

                        {/* Web Camera Button */}
                        <Button
                            variant="outlined"
                            startIcon={<CameraIcon />}
                            onClick={openCamera}
                            disabled={cameraOpen}
                            sx={{
                                borderColor: '#2196F3',
                                color: '#2196F3',
                                fontWeight: 600,
                                textTransform: 'none',
                                '&:hover': {
                                    borderColor: '#1976D2',
                                    bgcolor: 'rgba(33, 150, 243, 0.05)'
                                },
                                '&:disabled': {
                                    borderColor: '#ccc',
                                    color: '#ccc'
                                }
                            }}
                        >
                            {cameraOpen ? 'Camera Opening...' : t.openCamera}
                        </Button>

                        {/* Mobile Native Camera Button (fallback) */}
                        <Button
                            variant="outlined"
                            startIcon={<CameraIcon />}
                            component="label"
                            sx={{
                                borderColor: '#ff9800',
                                color: '#ff9800',
                                fontWeight: 600,
                                textTransform: 'none',
                                display: { xs: 'inline-flex', md: 'none' }, // Show only on mobile
                                '&:hover': {
                                    borderColor: '#f57c00',
                                    bgcolor: 'rgba(255, 152, 0, 0.05)'
                                }
                            }}
                        >
                            Take Photo
                            <input
                                type="file"
                                hidden
                                accept="image/*"
                                capture="environment"
                                onChange={handleImageChange}
                            />
                        </Button>
                    </Box>
                </CardContent>
            </Card>
        </Grid>

        {/* Right Column */}
        <Grid item xs={12} md={7}>
            <Card sx={{ height: '100%', bgcolor: 'rgba(255, 255, 255, 0.9)', backdropFilter: 'blur(10px)' }}>
                <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    {!selectedImage ? (
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', py: 4, border: 2, borderStyle: 'dashed', borderColor: 'rgba(76, 175, 80, 0.3)', borderRadius: 2, bgcolor: 'rgba(76, 175, 80, 0.05)' }}>
                            <UploadIcon sx={{ fontSize: 48, color: '#4caf50', mb: 2 }} />
                            <Typography variant="h6" gutterBottom sx={{ color: '#1B4C35', fontWeight: 600 }}>{t.uploadTitle}</Typography>
                            <Typography variant="body2" color="text.secondary" textAlign="center">{t.uploadDescription}</Typography>
                        </Box>
                    ) : (
                        <Box>
                            <Box sx={{ position: 'relative', mb: 3 }}>
                                <Box component="img" src={previewUrl || ''} alt="Plant preview" sx={{ width: '100%', height: '300px', objectFit: 'cover', borderRadius: 2, border: 1, borderColor: 'divider' }} />
                                <IconButton onClick={resetAnalysis} sx={{ position: 'absolute', top: 8, right: 8, bgcolor: 'rgba(0, 0, 0, 0.5)', color: 'white', '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.7)' } }}>
                                    <CloseIcon />
                                </IconButton>
                            </Box>
                            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', mb: 2 }}>
                                <Button variant="contained" onClick={analyzePlant} disabled={isAnalyzing} sx={{ bgcolor: '#4caf50', color: 'white', fontWeight: 600, textTransform: 'none', minWidth: 150, minHeight: 40, '&:hover': { bgcolor: '#43a047' } }}>
                                    {isAnalyzing ? t.analyzing : t.analyze}
                                </Button>
                                <Button variant="outlined" onClick={resetAnalysis} disabled={isAnalyzing} sx={{ borderColor: '#f44336', color: '#f44336', fontWeight: 600, textTransform: 'none', '&:hover': { borderColor: '#d32f2f', bgcolor: 'rgba(244, 67, 54, 0.05)' } }}>
                                    {t.reset}
                                </Button>
                            </Box>
                        </Box>
                    )}
                    {isAnalyzing && <LinearProgress color="success" sx={{ my: 2 }} />}
                    {error && (<Alert severity="error" icon={<ErrorIcon />} sx={{ mt: 2 }}>{error}</Alert>)}
                    {analysisResult && (
                        <Box sx={{ mt: 2 }}>
                            <Typography variant="h6" gutterBottom sx={{ color: '#1B4C35', fontWeight: 600, display: 'flex', alignItems: 'center' }}>
                                <CheckCircleIcon sx={{ color: '#4caf50', mr: 1 }} />
                                {t.result}
                            </Typography>
                            <Box sx={{ p: 2, bgcolor: 'rgba(76, 175, 80, 0.05)', borderRadius: 2, border: 1, borderColor: 'rgba(76, 175, 80, 0.2)' }}>
                                <Typography variant="body2" sx={{ lineHeight: 1.7 }} dangerouslySetInnerHTML={renderResult(analysisResult)} />
                            </Box>
                        </Box>
                    )}
                </CardContent>
            </Card>
        </Grid>
      </Grid>

      {/* Camera Dialog */}
      <Dialog open={cameraOpen} onClose={closeCamera} maxWidth="md" fullWidth>
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>
            <Typography variant="h6">{t.cameraTitle}</Typography>
            <IconButton onClick={closeCamera}><CloseIcon /></IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 0, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '300px' }}>
            {cameraError ? (
                <Box sx={{ p: 3, textAlign: 'center' }}>
                    <ErrorIcon sx={{ fontSize: 48, color: 'error.main', mb: 2 }} />
                    <Typography variant="body1" color="error.main" gutterBottom>{t.cameraError}</Typography>
                </Box>
            ) : (
                <Box sx={{
                    position: 'relative',
                    bgcolor: 'black',
                    width: '100%',
                    minHeight: '300px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: 1,
                    overflow: 'hidden'
                }}>
                    {/* Loading indicator while camera initializes */}
                    {!isVideoReady && (
                        <Box sx={{
                            position: 'absolute',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            color: 'white',
                            zIndex: 2
                        }}>
                            <CircularProgress color="inherit" sx={{ mb: 2 }} />
                            <Typography variant="body2">Initializing camera...</Typography>
                        </Box>
                    )}

                    <video
                        ref={videoRef}
                        autoPlay
                        playsInline
                        muted
                        style={{
                            width: '100%',
                            height: 'auto',
                            maxHeight: '400px',
                            objectFit: 'cover',
                            display: isVideoReady ? 'block' : 'none'
                        }}
                        onLoadedMetadata={() => {
                            setIsVideoReady(true);
                        }}
                        onCanPlay={() => {
                            if (videoRef.current) {
                                videoRef.current.play().catch(() => {
                                    // Handle auto-play error silently
                                });
                            }
                        }}
                        onError={() => {
                            setCameraError(true);
                            setError('Error loading camera feed. Please try again.');
                        }}
                    />

                    {/* Hidden canvas for capturing photos */}
                    <canvas
                        ref={canvasRef}
                        style={{
                            position: 'absolute',
                            top: '-9999px',
                            left: '-9999px',
                            visibility: 'hidden'
                        }}
                    />

                    {/* Camera overlay for better UX */}
                    {isVideoReady && (
                        <Box sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            border: '2px solid rgba(255, 255, 255, 0.5)',
                            borderRadius: 1,
                            pointerEvents: 'none'
                        }} />
                    )}
                </Box>
            )}
        </DialogContent>
        {!cameraError && (
            <DialogActions sx={{ p: 2, display: 'flex', justifyContent: 'space-between' }}>
                <Button
                    onClick={closeCamera}
                    variant="outlined"
                    sx={{ minWidth: 100 }}
                >
                    {t.cancel}
                </Button>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {!isVideoReady && (
                        <Typography variant="body2" color="text.secondary">
                            Waiting for camera...
                        </Typography>
                    )}
                    <Button
                        onClick={capturePhoto}
                        variant="contained"
                        disabled={!isVideoReady}
                        startIcon={<CameraIcon />}
                        sx={{
                            bgcolor: '#4caf50',
                            minWidth: 120,
                            '&:hover': { bgcolor: '#43a047' },
                            '&:disabled': {
                                bgcolor: '#ccc',
                                color: '#666'
                            }
                        }}
                    >
                        {isVideoReady ? t.capture : 'Please Wait...'}
                    </Button>
                </Box>
            </DialogActions>
        )}
      </Dialog>
    </Box>
  );
};

export default PlantAnalysis;