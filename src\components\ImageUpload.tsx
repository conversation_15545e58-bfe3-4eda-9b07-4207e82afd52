import React, { useState, useRef, useCallback } from 'react';
import {
  Box,
  Button,
  Typography,
  IconButton,
  Card,
  CardMedia,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  Alert,
  Grid,
  Tooltip
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Preview as PreviewIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Styled components
const UploadArea = styled(Box)(({ theme }) => ({
  border: `2px dashed ${theme.palette.primary.main}`,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(4),
  textAlign: 'center',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    borderColor: theme.palette.primary.dark,
    backgroundColor: theme.palette.action.hover,
  },
  '&.dragover': {
    borderColor: theme.palette.secondary.main,
    backgroundColor: theme.palette.secondary.light + '20',
  }
}));

const ImagePreview = styled(CardMedia)(({ theme }) => ({
  height: 200,
  objectFit: 'cover',
  cursor: 'pointer',
  transition: 'transform 0.3s ease',
  '&:hover': {
    transform: 'scale(1.05)',
  }
}));

interface ImageData {
  id: string;
  file?: File;
  url: string;
  title: string;
  alt: string;
  description?: string;
  category?: string;
  tags?: string[];
}

interface ImageUploadProps {
  images: ImageData[];
  onImagesChange: (images: ImageData[]) => void;
  maxImages?: number;
  acceptedFormats?: string[];
  maxFileSize?: number; // in bytes
  showCategories?: boolean;
  categories?: string[];
  multiple?: boolean;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  images,
  onImagesChange,
  maxImages = 10,
  acceptedFormats = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  maxFileSize = 10 * 1024 * 1024, // 10MB
  showCategories = true,
  categories = ['hero', 'gallery', 'team', 'products', 'blog', 'general'],
  multiple = true
}) => {
  const [dragOver, setDragOver] = useState(false);
  const [editingImage, setEditingImage] = useState<ImageData | null>(null);
  const [previewImage, setPreviewImage] = useState<ImageData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileSelect = useCallback((files: FileList) => {
    const newImages: ImageData[] = [];
    const errors: string[] = [];

    Array.from(files).forEach((file, index) => {
      // Validate file type
      if (!acceptedFormats.includes(file.type)) {
        errors.push(`${file.name}: Invalid file format`);
        return;
      }

      // Validate file size
      if (file.size > maxFileSize) {
        errors.push(`${file.name}: File too large (max ${maxFileSize / 1024 / 1024}MB)`);
        return;
      }

      // Check if we're at max images
      if (!multiple && images.length > 0) {
        errors.push('Only one image allowed');
        return;
      }

      if (images.length + newImages.length >= maxImages) {
        errors.push(`Maximum ${maxImages} images allowed`);
        return;
      }

      const imageData: ImageData = {
        id: `${Date.now()}-${index}`,
        file,
        url: URL.createObjectURL(file),
        title: file.name.replace(/\.[^/.]+$/, ""),
        alt: file.name.replace(/\.[^/.]+$/, ""),
        category: categories[0]
      };

      newImages.push(imageData);
    });

    if (errors.length > 0) {
      setError(errors.join(', '));
    } else {
      setError(null);
    }

    if (newImages.length > 0) {
      onImagesChange([...images, ...newImages]);
    }
  }, [images, onImagesChange, acceptedFormats, maxFileSize, maxImages, multiple, categories]);

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect]);

  // Handle file input click
  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files);
    }
  };

  // Delete image
  const handleDeleteImage = (id: string) => {
    const updatedImages = images.filter(img => img.id !== id);
    onImagesChange(updatedImages);
  };

  // Edit image metadata
  const handleEditImage = (image: ImageData) => {
    setEditingImage({ ...image });
  };

  // Save edited image
  const handleSaveEdit = () => {
    if (!editingImage) return;

    const updatedImages = images.map(img => 
      img.id === editingImage.id ? editingImage : img
    );
    onImagesChange(updatedImages);
    setEditingImage(null);
  };

  // Preview image
  const handlePreviewImage = (image: ImageData) => {
    setPreviewImage(image);
  };

  return (
    <Box>
      {/* Upload Area */}
      <UploadArea
        className={dragOver ? 'dragover' : ''}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleUploadClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept={acceptedFormats.join(',')}
          onChange={handleFileInputChange}
          style={{ display: 'none' }}
        />
        
        <UploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          {dragOver ? 'Drop images here' : 'Click or drag images to upload'}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Supported formats: {acceptedFormats.map(f => f.split('/')[1]).join(', ')}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Max file size: {maxFileSize / 1024 / 1024}MB
        </Typography>
        {multiple && (
          <Typography variant="body2" color="text.secondary">
            Max images: {maxImages}
          </Typography>
        )}
      </UploadArea>

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mt: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Image Grid */}
      {images.length > 0 && (
        <Grid container spacing={2} sx={{ mt: 2 }}>
          {images.map((image) => (
            <Grid item xs={12} sm={6} md={4} key={image.id}>
              <Card>
                <ImagePreview
                  image={image.url}
                  title={image.title}
                  onClick={() => handlePreviewImage(image)}
                />
                <CardActions>
                  <Tooltip title="Preview">
                    <IconButton size="small" onClick={() => handlePreviewImage(image)}>
                      <PreviewIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Edit">
                    <IconButton size="small" onClick={() => handleEditImage(image)}>
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Delete">
                    <IconButton size="small" onClick={() => handleDeleteImage(image.id)}>
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </CardActions>
                <Box sx={{ p: 1 }}>
                  <Typography variant="caption" noWrap>
                    {image.title}
                  </Typography>
                  {image.category && (
                    <Chip label={image.category} size="small" sx={{ ml: 1 }} />
                  )}
                </Box>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Edit Dialog */}
      <Dialog open={!!editingImage} onClose={() => setEditingImage(null)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Image Details</DialogTitle>
        <DialogContent>
          {editingImage && (
            <Box sx={{ pt: 1 }}>
              <TextField
                fullWidth
                label="Title"
                value={editingImage.title}
                onChange={(e) => setEditingImage({ ...editingImage, title: e.target.value })}
                margin="normal"
              />
              <TextField
                fullWidth
                label="Alt Text"
                value={editingImage.alt}
                onChange={(e) => setEditingImage({ ...editingImage, alt: e.target.value })}
                margin="normal"
              />
              <TextField
                fullWidth
                label="Description"
                value={editingImage.description || ''}
                onChange={(e) => setEditingImage({ ...editingImage, description: e.target.value })}
                margin="normal"
                multiline
                rows={3}
              />
              {showCategories && (
                <TextField
                  fullWidth
                  select
                  label="Category"
                  value={editingImage.category || ''}
                  onChange={(e) => setEditingImage({ ...editingImage, category: e.target.value })}
                  margin="normal"
                  SelectProps={{ native: true }}
                >
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </TextField>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditingImage(null)}>Cancel</Button>
          <Button onClick={handleSaveEdit} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>

      {/* Preview Dialog */}
      <Dialog open={!!previewImage} onClose={() => setPreviewImage(null)} maxWidth="md" fullWidth>
        <DialogTitle>
          Image Preview
          <IconButton
            onClick={() => setPreviewImage(null)}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          {previewImage && (
            <Box sx={{ textAlign: 'center' }}>
              <img
                src={previewImage.url}
                alt={previewImage.alt}
                style={{ maxWidth: '100%', maxHeight: '70vh', objectFit: 'contain' }}
              />
              <Typography variant="h6" sx={{ mt: 2 }}>
                {previewImage.title}
              </Typography>
              {previewImage.description && (
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  {previewImage.description}
                </Typography>
              )}
            </Box>
          )}
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default ImageUpload;
