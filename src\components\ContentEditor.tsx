import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  TextField,
  Button,
  Grid,
  Tabs,
  Tab,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Preview as PreviewIcon,
  Language as LanguageIcon
} from '@mui/icons-material';
import ImageUpload from './ImageUpload';



interface ContentSection {
  id: string;
  type: 'text' | 'multitext' | 'image' | 'list' | 'object';
  label: string;
  value: any;
  required?: boolean;
  multilingual?: boolean;
}

interface ContentEditorProps {
  title: string;
  sections: ContentSection[];
  onSave: (data: any) => Promise<void>;
  onPreview?: (data: any) => void;
  loading?: boolean;
  error?: string | null;
}

const ContentEditor: React.FC<ContentEditorProps> = ({
  title,
  sections: initialSections,
  onSave,
  onPreview,
  loading = false,
  error = null
}) => {
  const [sections, setSections] = useState<ContentSection[]>(initialSections);
  const [activeLanguage, setActiveLanguage] = useState<'en' | 'kn' | 'hi'>('en');
  const [saving, setSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [previewDialog, setPreviewDialog] = useState(false);
  const [expandedSections, setExpandedSections] = useState<string[]>([]);

  useEffect(() => {
    setSections(initialSections);
  }, [initialSections]);

  // Handle section value change
  const handleSectionChange = (sectionId: string, newValue: any) => {
    setSections(prev => prev.map(section => 
      section.id === sectionId ? { ...section, value: newValue } : section
    ));
  };

  // Handle multilingual text change
  const handleMultilingualChange = (sectionId: string, language: 'en' | 'kn' | 'hi', value: string) => {
    setSections(prev => prev.map(section => {
      if (section.id === sectionId && section.multilingual) {
        return {
          ...section,
          value: {
            ...section.value,
            [language]: value
          }
        };
      }
      return section;
    }));
  };

  // Handle save
  const handleSave = async () => {
    setSaving(true);
    try {
      const data = sections.reduce((acc, section) => {
        acc[section.id] = section.value;
        return acc;
      }, {} as any);

      await onSave(data);
      setSaveSuccess(true);
      setTimeout(() => setSaveSuccess(false), 3000);
    } catch (err) {
      // Handle save error silently in production
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('Save failed:', err);
      }
    } finally {
      setSaving(false);
    }
  };

  // Handle preview
  const handlePreview = () => {
    if (onPreview) {
      const data = sections.reduce((acc, section) => {
        acc[section.id] = section.value;
        return acc;
      }, {} as any);
      onPreview(data);
    }
    setPreviewDialog(true);
  };

  // Toggle section expansion
  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  // Render different field types
  const renderField = (section: ContentSection) => {
    switch (section.type) {
      case 'text':
        if (section.multilingual) {
          return (
            <Box>
              <Box sx={{ mb: 2 }}>
                <Tabs value={activeLanguage} onChange={(_, value) => setActiveLanguage(value)}>
                  <Tab label="English" value="en" />
                  <Tab label="ಕನ್ನಡ" value="kn" />
                  <Tab label="हिंदी" value="hi" />
                </Tabs>
              </Box>
              <TextField
                fullWidth
                multiline
                rows={4}
                value={section.value?.[activeLanguage] || ''}
                onChange={(e) => handleMultilingualChange(section.id, activeLanguage, e.target.value)}
                placeholder={`Enter ${section.label} in ${activeLanguage === 'en' ? 'English' : activeLanguage === 'kn' ? 'Kannada' : 'Hindi'}`}
              />
            </Box>
          );
        }
        return (
          <TextField
            fullWidth
            multiline
            rows={4}
            value={section.value || ''}
            onChange={(e) => handleSectionChange(section.id, e.target.value)}
            placeholder={`Enter ${section.label}`}
          />
        );

      case 'image':
        return (
          <ImageUpload
            images={section.value || []}
            onImagesChange={(images) => handleSectionChange(section.id, images)}
            multiple={false}
          />
        );

      case 'list':
        return (
          <Box>
            {(section.value || []).map((item: any, index: number) => (
              <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <TextField
                  fullWidth
                  value={item}
                  onChange={(e) => {
                    const newList = [...(section.value || [])];
                    newList[index] = e.target.value;
                    handleSectionChange(section.id, newList);
                  }}
                  placeholder={`${section.label} item ${index + 1}`}
                />
                <IconButton
                  onClick={() => {
                    const newList = (section.value || []).filter((_: any, i: number) => i !== index);
                    handleSectionChange(section.id, newList);
                  }}
                >
                  <DeleteIcon />
                </IconButton>
              </Box>
            ))}
            <Button
              startIcon={<AddIcon />}
              onClick={() => {
                const newList = [...(section.value || []), ''];
                handleSectionChange(section.id, newList);
              }}
            >
              Add Item
            </Button>
          </Box>
        );

      default:
        return (
          <TextField
            fullWidth
            value={section.value || ''}
            onChange={(e) => handleSectionChange(section.id, e.target.value)}
            placeholder={`Enter ${section.label}`}
          />
        );
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">{title}</Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          {onPreview && (
            <Button
              variant="outlined"
              startIcon={<PreviewIcon />}
              onClick={handlePreview}
            >
              Preview
            </Button>
          )}
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? 'Saving...' : 'Save'}
          </Button>
        </Box>
      </Box>

      {/* Success/Error Messages */}
      {saveSuccess && (
        <Alert severity="success" sx={{ mb: 2 }}>
          Content saved successfully!
        </Alert>
      )}
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Content Sections */}
      <Grid container spacing={3}>
        {sections.map((section) => (
          <Grid item xs={12} key={section.id}>
            <Accordion 
              expanded={expandedSections.includes(section.id)}
              onChange={() => toggleSection(section.id)}
            >
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="h6">{section.label}</Typography>
                  {section.required && (
                    <Chip label="Required" size="small" color="error" />
                  )}
                  {section.multilingual && (
                    <Chip 
                      label="Multilingual" 
                      size="small" 
                      color="primary" 
                      icon={<LanguageIcon />} 
                    />
                  )}
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                {renderField(section)}
              </AccordionDetails>
            </Accordion>
          </Grid>
        ))}
      </Grid>

      {/* Preview Dialog */}
      <Dialog 
        open={previewDialog} 
        onClose={() => setPreviewDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Content Preview</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary">
            Preview functionality would show how the content appears on the live site.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ContentEditor;
