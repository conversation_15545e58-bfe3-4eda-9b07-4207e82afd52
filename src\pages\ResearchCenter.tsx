import React, { useEffect, useState } from 'react';
import { 
  <PERSON>, 
  Typography, 
  Container, 
  Grid, 
  <PERSON><PERSON>, 
  Card, 
  CardContent,
  Chip,
  Avatar,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  keyframes
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { Link } from 'react-router-dom';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ForestIcon from '@mui/icons-material/Forest';
import NatureIcon from '@mui/icons-material/Nature';
import LocalFloristIcon from '@mui/icons-material/LocalFlorist';
import ScienceIcon from '@mui/icons-material/Science';
import AgroIcon from '@mui/icons-material/Agriculture';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import TimelineIcon from '@mui/icons-material/Timeline';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import PublicIcon from '@mui/icons-material/Public';
import Footer from '../components/Footer';
import SectionContainer from '../components/SectionContainerFixed';
import { useLanguage } from '../contexts/LanguageContext';

// Animation keyframes
const fadeInUp = keyframes`
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const slideInLeft = keyframes`
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`;

const slideInRight = keyframes`
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
`;

const float = keyframes`
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
`;

const ResearchCenter: React.FC = () => {
  const theme = useTheme();
  const { language } = useLanguage();
  const [animate, setAnimate] = useState(false);

  useEffect(() => {
    document.title = language === 'en' ? 'Research Center | Darvi Group' :
                     language === 'kn' ? 'ಸಂಶೋಧನಾ ಕೇಂದ್ರ | ದಾರ್ವಿ ಗ್ರೂಪ್' :
                     'अनुसंधान केंद्र | दारवी ग्रुप';
    window.scrollTo(0, 0);
    
    // Trigger animations after component mounts
    const timer = setTimeout(() => setAnimate(true), 100);
    return () => clearTimeout(timer);
  }, [language]);

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {/* Hero Section */}
      <Box
        sx={{
          position: 'relative',
          height: { xs: '300px', sm: '400px', md: '500px' },
          backgroundColor: '#1B4C35',
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: 'url(/darvi-images/field1.png)',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            opacity: 0.2,
            zIndex: 0
          },
          '&::after': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'linear-gradient(135deg, rgba(27, 76, 53, 0.9) 0%, rgba(46, 125, 50, 0.8) 100%)',
            zIndex: 1
          }
        }}
      >
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2, textAlign: 'center' }}>
          <Box
            sx={{
              animation: animate ? `${fadeInUp} 1s ease-out` : 'none',
              mb: 3
            }}
          >
            <Avatar
              sx={{
                mx: 'auto',
                mb: 2,
                width: { xs: 80, sm: 100, md: 120 },
                height: { xs: 80, sm: 100, md: 120 },
                bgcolor: 'rgba(255,255,255,0.2)',
                backdropFilter: 'blur(10px)',
                border: '2px solid rgba(255,255,255,0.3)',
                animation: `${pulse} 3s infinite`
              }}
            >
              <ScienceIcon sx={{ fontSize: { xs: 40, sm: 50, md: 60 } }} />
            </Avatar>
          </Box>
          
          <Typography 
            variant="h2" 
            component="h1" 
            sx={{ 
              fontWeight: 700, 
              mb: 2,
              fontSize: { xs: '2rem', sm: '2.5rem', md: '3.5rem' },
              textShadow: '0 4px 8px rgba(0,0,0,0.3)',
              animation: animate ? `${fadeInUp} 1s ease-out 0.2s both` : 'none',
              background: 'linear-gradient(45deg, #fff, #e8f5e8)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}
          >
            {language === 'en' ? 'Research Center' :
             language === 'kn' ? 'ಸಂಶೋಧನಾ ಕೇಂದ್ರ' :
             'अनुसंधान केंद्र'}
          </Typography>
          <Typography 
            variant="h5" 
            sx={{ 
              fontWeight: 400,
              maxWidth: '800px',
              mx: 'auto',
              fontSize: { xs: '1rem', sm: '1.25rem', md: '1.5rem' },
              opacity: 0.95,
              textShadow: '0 2px 4px rgba(0,0,0,0.2)',
              animation: animate ? `${fadeInUp} 1s ease-out 0.4s both` : 'none',
              lineHeight: 1.4
            }}
          >
            {language === 'en' ? 'Exploring sustainable and innovative agricultural practices' :
             language === 'kn' ? 'ಸುಸ್ಥಿರ ಮತ್ತು ನವೀನ ಕೃಷಿ ಅಭ್ಯಾಸಗಳನ್ನು ಅನ್ವೇಷಿಸುವುದು' :
             'स्थायी और नवीन कृषि प्रथाओं का अन्वेषण'}
          </Typography>
        </Container>
      </Box>

      {/* Introduction Section */}
      <SectionContainer
        title={language === 'en' ? 'Our Research Focus' :
               language === 'kn' ? 'ನಮ್ಮ ಸಂಶೋಧನಾ ಕೇಂದ್ರ' :
               'हमारा अनुसंधान केंद्र'}
        subtitle={language === 'en' ? 'Exclusive research on high-value crops for sustainable agriculture' :
                  language === 'kn' ? 'ಸುಸ್ಥಿರ ಕೃಷಿಗಾಗಿ ಅಧಿಕ ಮೌಲ್ಯದ ಬೆಳೆಗಳ ಬಗ್ಗೆ ವಿಶೇಷ ಸಂಶೋಧನೆ' :
                  'स्थायी कृषि के लिए उच्च मूल्य वाली फसलों पर विशेष अनुसंधान'}
        bgColor="#f9f9f9"
        textColor="#333"
        paddingTop={5}
        paddingBottom={5}
      >
        <Typography variant="body1" sx={{ mb: 4, textAlign: 'center', maxWidth: '900px', mx: 'auto' }}>
          {language === 'en' ? 
            'At Darvi Group Research Center, we focus on developing sustainable agricultural practices for high-value crops. Our research aims to maximize yield, quality, and environmental sustainability through innovative techniques and careful analysis.' :
           language === 'kn' ? 
            'ದಾರ್ವಿ ಗ್ರೂಪ್ ಸಂಶೋಧನಾ ಕೇಂದ್ರದಲ್ಲಿ, ನಾವು ಅಧಿಕ ಮೌಲ್ಯದ ಬೆಳೆಗಳಿಗಾಗಿ ಸುಸ್ಥಿರ ಕೃಷಿ ಅಭ್ಯಾಸಗಳನ್ನು ಅಭಿವೃದ್ಧಿಪಡಿಸುವ ಮೇಲೆ ಕೇಂದ್ರೀಕರಿಸುತ್ತೇವೆ. ನಮ್ಮ ಸಂಶೋಧನೆಯು ನವೀನ ತಂತ್ರಗಳು ಮತ್ತು ಎಚ್ಚರಿಕೆಯ ವಿಶ್ಲೇಷಣೆಯ ಮೂಲಕ ಇಳುವರಿ, ಗುಣಮಟ್ಟ ಮತ್ತು ಪರಿಸರ ಸುಸ್ಥಿರತೆಯನ್ನು ಗರಿಷ್ಠಗೊಳಿಸುವ ಗುರಿಯನ್ನು ಹೊಂದಿದೆ.' :
            'दारवी ग्रुप रिसर्च सेंटर में, हम उच्च मूल्य वाली फसलों के लिए स्थायी कृषि प्रथाओं को विकसित करने पर ध्यान केंद्रित करते हैं। हमारा अनुसंधान नवीन तकनीकों और सावधानीपूर्वक विश्लेषण के माध्यम से उपज, गुणवत्ता और पर्यावरणीय स्थिरता को अधिकतम करने का लक्ष्य रखता है।'}
        </Typography>
      </SectionContainer>

      {/* Sandalwood Section */}
      <Box id="sandalwood" sx={{ py: 6, backgroundColor: '#fff' }}>
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <Box 
                sx={{ 
                  position: 'relative', 
                  height: '100%', 
                  minHeight: '300px',
                  animation: animate ? `${slideInLeft} 1s ease-out` : 'none'
                }}
              >
                <Box
                  component="img"
                  src="/images/sandalwood.svg"
                  alt="Sandalwood"
                  sx={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    borderRadius: 2,
                    boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'scale(1.02)',
                      boxShadow: '0 15px 40px rgba(0,0,0,0.15)'
                    }
                  }}
                />
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: -20,
                    right: -20,
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    backgroundColor: theme.palette.primary.main,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 5px 15px rgba(0,0,0,0.2)',
                    zIndex: 1,
                    animation: `${float} 3s ease-in-out infinite`
                  }}
                >
                  <ForestIcon sx={{ fontSize: 40, color: 'white' }} />
                </Box>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ animation: animate ? `${slideInRight} 1s ease-out 0.2s both` : 'none' }}>
                <Typography variant="h3" component="h2" gutterBottom sx={{ 
                  color: theme.palette.primary.main, 
                  fontWeight: 700,
                  textShadow: '0 2px 4px rgba(0,0,0,0.1)',
                  mb: 2
                }}>
                  {language === 'en' ? 'Sandalwood' :
                   language === 'kn' ? 'ಗಂಧದ ಮರ' :
                   'चंदन'}
                </Typography>
                <Typography variant="h6" gutterBottom sx={{ 
                  color: '#666', 
                  mb: 3,
                  fontWeight: 500,
                  lineHeight: 1.4
                }}>
                  {language === 'en' ? 'Sustainable cultivation of premium sandalwood' :
                   language === 'kn' ? 'ಪ್ರೀಮಿಯಂ ಗಂಧದ ಮರದ ಸುಸ್ಥಿರ ಕೃಷಿ' :
                   'प्रीमियम चंदन की स्थायी खेती'}
                </Typography>
                              <Typography variant="body1" paragraph>
                  {language === 'en' ? 
                    'Sandalwood (Santalum album) is one of the most valuable trees in the world, prized for its aromatic heartwood and essential oil. Our research focuses on sustainable cultivation methods, disease management, and optimizing growth conditions for this precious resource.' :
                   language === 'kn' ? 
                    'ಗಂಧದ ಮರ (ಸಾಂಟಾಲಮ್ ಆಲ್ಬಮ್) ಪ್ರಪಂಚದಲ್ಲಿ ಅತ್ಯಂತ ಮೌಲ್ಯಯುತ ಮರಗಳಲ್ಲಿ ಒಂದಾಗಿದೆ, ಇದು ಸುಗಂಧಿತ ಹೃದಯದ ಮರ ಮತ್ತು ಅಗತ್ಯ ತೈಲಕ್ಕಾಗಿ ಪ್ರಶಂಸಿಸಲ್ಪಟ್ಟಿದೆ. ನಮ್ಮ ಸಂಶೋಧನೆಯು ಸುಸ್ಥಿರ ಕೃಷಿ ವಿಧಾನಗಳು, ರೋಗ ನಿರ್ವಹಣೆ ಮತ್ತು ಈ ಅಮೂಲ್ಯ ಸಂಪನ್ಮೂಲಕ್ಕಾಗಿ ಬೆಳವಣಿಗೆಯ ಪರಿಸ್ಥಿತಿಗಳನ್ನು ಅನುಕೂಲಿಸುವ ಮೇಲೆ ಕೇಂದ್ರೀಕರಿಸುತ್ತದೆ.' :
                    'चंदन (सैंटालम एल्बम) दुनिया के सबसे मूल्यवान पेड़ों में से एक है, जिसे इसकी सुगंधित हार्टवुड और आवश्यक तेल के लिए सराहा जाता है। हमारा अनुसंधान स्थायी खेती विधियों, रोग प्रबंधन और इस कीमती संसाधन के लिए विकास की स्थितियों को अनुकूलित करने पर केंद्रित है।'}
                </Typography>
              </Box>
              <Typography variant="body1" paragraph>
                {language === 'en' ? 
                  'Benefits of our sandalwood research include:' :
                 language === 'kn' ? 
                  'ನಮ್ಮ ಗಂಧದ ಮರದ ಸಂಶೋಧನೆಯ ಪ್ರಯೋಜನಗಳು:' :
                  'हमारे चंदन अनुसंधान के लाभों में शामिल हैं:'}
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                  {language === 'en' ? 
                    'Reduced cultivation time from 30 to 15-20 years' :
                   language === 'kn' ? 
                    'ಕೃಷಿ ಸಮಯವನ್ನು 30 ರಿಂದ 15-20 ವರ್ಷಗಳಿಗೆ ಕಡಿಮೆ ಮಾಡಲಾಗಿದೆ' :
                    'खेती के समय को 30 से 15-20 वर्षों तक कम किया गया'}
                </Typography>
                <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                  {language === 'en' ? 
                    'Higher oil content and quality through specialized care' :
                   language === 'kn' ? 
                    'ವಿಶೇಷ ಆರೈಕೆಯ ಮೂಲಕ ಹೆಚ್ಚಿನ ತೈಲ ಸಾಮಗ್ರಿ ಮತ್ತು ಗುಣಮಟ್ಟ' :
                    'विशेष देखभाल के माध्यम से उच्च तेल सामग्री और गुणवत्ता'}
                </Typography>
                <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                  {language === 'en' ? 
                    'Sustainable harvesting techniques that preserve tree populations' :
                   language === 'kn' ? 
                    'ಮರದ ಜನಸಂಖ್ಯೆಯನ್ನು ಸಂರಕ್ಷಿಸುವ ಸುಸ್ಥಿರ ಕಟಾವು ತಂತ್ರಗಳು' :
                    'स्थायी कटाई तकनीकें जो पेड़ की आबादी को संरक्षित करती हैं'}
                </Typography>
                <Typography component="li" variant="body1">
                  {language === 'en' ? 
                    'Disease-resistant varieties developed through careful selection' :
                   language === 'kn' ? 
                    'ಎಚ್ಚರಿಕೆಯಿಂದ ಆಯ್ಕೆ ಮಾಡುವ ಮೂಲಕ ಅಭಿವೃದ್ಧಿಪಡಿಸಿದ ರೋಗ ನಿರೋಧಕ ವಿವಿಧತೆಗಳು' :
                    'सावधानीपूर्वक चयन के माध्यम से विकसित रोग प्रतिरोधी किस्में'}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Bamboo Section */}
      <Box id="bamboo" sx={{ py: 6, backgroundColor: '#f5f9f5' }}>
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center" direction={{ xs: 'column-reverse', md: 'row' }}>
            <Grid item xs={12} md={6}>
              <Typography variant="h3" component="h2" gutterBottom sx={{ color: theme.palette.primary.main, fontWeight: 700 }}>
                {language === 'en' ? 'Bamboo' :
                 language === 'kn' ? 'ಬಿದಿರು' :
                 'बांस'}
              </Typography>
              <Typography variant="h6" gutterBottom sx={{ color: '#666', mb: 3 }}>
                {language === 'en' ? 'Fast-growing sustainable resource' :
                 language === 'kn' ? 'ವೇಗವಾಗಿ ಬೆಳೆಯುವ ಸುಸ್ಥಿರ ಸಂಪನ್ಮೂಲ' :
                 'तेजी से बढ़ने वाला स्थायी संसाधन'}
              </Typography>
              <Typography variant="body1" paragraph>
                {language === 'en' ? 
                  'Bamboo is one of the fastest-growing plants on Earth, making it an excellent renewable resource. Our research center focuses on cultivating various bamboo species for different applications, from construction materials to textiles and food products.' :
                 language === 'kn' ? 
                  'ಬಿದಿರು ಭೂಮಿಯ ಮೇಲೆ ಅತ್ಯಂತ ವೇಗವಾಗಿ ಬೆಳೆಯುವ ಸಸ್ಯಗಳಲ್ಲಿ ಒಂದಾಗಿದೆ, ಇದು ಅದನ್ನು ಅತ್ಯುತ್ತಮ ನವೀಕರಿಸಬಹುದಾದ ಸಂಪನ್ಮೂಲವನ್ನಾಗಿ ಮಾಡುತ್ತದೆ. ನಮ್ಮ ಸಂಶೋಧನಾ ಕೇಂದ್ರವು ನಿರ್ಮಾಣ ವಸ್ತುಗಳಿಂದ ಜವಳಿ ಮತ್ತು ಆಹಾರ ಉತ್ಪನ್ನಗಳವರೆಗೆ ವಿವಿಧ ಅಪ್ಲಿಕೇಶನ್‌ಗಳಿಗಾಗಿ ವಿವಿಧ ಬಿದಿರು ಪ್ರಭೇದಗಳನ್ನು ಬೆಳೆಸುವ ಮೇಲೆ ಕೇಂದ್ರೀಕರಿಸುತ್ತದೆ.' :
                  'बांस पृथ्वी पर सबसे तेजी से बढ़ने वाले पौधों में से एक है, जिससे यह एक उत्कृष्ट नवीकरणीय संसाधन बन जाता है। हमारा अनुसंधान केंद्र निर्माण सामग्री से लेकर कपड़े और खाद्य उत्पादों तक विभिन्न अनुप्रयोगों के लिए विभिन्न बांस प्रजातियों की खेती पर केंद्रित है।'}
              </Typography>
              <Typography variant="body1" paragraph>
                {language === 'en' ? 
                  'Key aspects of our bamboo research:' :
                 language === 'kn' ? 
                  'ನಮ್ಮ ಬಿದಿರು ಸಂಶೋಧನೆಯ ಪ್ರಮುಖ ಅಂಶಗಳು:' :
                  'हमारे बांस अनुसंधान के प्रमुख पहलू:'}
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                  {language === 'en' ? 
                    'Identification of fast-growing, high-yield bamboo varieties' :
                   language === 'kn' ? 
                    'ವೇಗವಾಗಿ ಬೆಳೆಯುವ, ಹೆಚ್ಚಿನ ಇಳುವರಿಯ ಬಿದಿರು ವಿವಿಧತೆಗಳ ಗುರುತಿಸುವಿಕೆ' :
                    'तेजी से बढ़ने वाली, उच्च उपज वाली बांस किस्मों की पहचान'}
                </Typography>
                <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                  {language === 'en' ? 
                    'Sustainable harvesting techniques that maintain bamboo groves' :
                   language === 'kn' ? 
                    'ಬಿದಿರು ತೋಪುಗಳನ್ನು ನಿರ್ವಹಿಸುವ ಸುಸ್ಥಿರ ಕಟಾವು ತಂತ್ರಗಳು' :
                    'स्थायी कटाई तकनीकें जो बांस के झुरमुटों को बनाए रखती हैं'}
                </Typography>
                <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                  {language === 'en' ? 
                    'Processing methods for various bamboo products and applications' :
                   language === 'kn' ? 
                    'ವಿವಿಧ ಬಿದಿರು ಉತ್ಪನ್ನಗಳು ಮತ್ತು ಅಪ್ಲಿಕೇಶನ್‌ಗಳಿಗಾಗಿ ಸಂಸ್ಕರಣ ವಿಧಾನಗಳು' :
                    'विभिन्न बांस उत्पादों और अनुप्रयोगों के लिए प्रसंस्करण विधियां'}
                </Typography>
                <Typography component="li" variant="body1">
                  {language === 'en' ? 
                    'Carbon sequestration studies showing bamboo\'s environmental benefits' :
                   language === 'kn' ? 
                    'ಬಿದಿರಿನ ಪರಿಸರ ಪ್ರಯೋಜನಗಳನ್ನು ತೋರಿಸುವ ಕಾರ್ಬನ್ ಸೀಕ್ವೆಸ್ಟ್ರೇಶನ್ ಅಧ್ಯಯನಗಳು' :
                    'कार्बन अनुक्रमण अध्ययन जो बांस के पर्यावरणीय लाभों को दिखाते हैं'}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ position: 'relative', height: '100%', minHeight: '300px' }}>
                <Box
                  component="img"
                  src="/images/bamboo.svg"
                  alt="Bamboo"
                  sx={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    borderRadius: 2,
                    boxShadow: '0 10px 30px rgba(0,0,0,0.1)'
                  }}
                />
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: -20,
                    left: -20,
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    backgroundColor: theme.palette.primary.main,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 5px 15px rgba(0,0,0,0.2)',
                    zIndex: 1
                  }}
                >
                  <NatureIcon sx={{ fontSize: 40, color: 'white' }} />
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Avocado Section */}
      <Box id="avocado" sx={{ py: 6, backgroundColor: '#fff' }}>
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <Box sx={{ position: 'relative', height: '100%', minHeight: '300px' }}>
                <Box
                  component="img"
                  src="/images/avocado.svg"
                  alt="Avocado"
                  sx={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    borderRadius: 2,
                    boxShadow: '0 10px 30px rgba(0,0,0,0.1)'
                  }}
                />
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: -20,
                    right: -20,
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    backgroundColor: theme.palette.primary.main,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 5px 15px rgba(0,0,0,0.2)',
                    zIndex: 1
                  }}
                >
                  <LocalFloristIcon sx={{ fontSize: 40, color: 'white' }} />
                </Box>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h3" component="h2" gutterBottom sx={{ color: theme.palette.primary.main, fontWeight: 700 }}>
                {language === 'en' ? 'Avocado' :
                 language === 'kn' ? 'ಅವಕಾಡೊ' :
                 'एवोकाडो'}
              </Typography>
              <Typography variant="h6" gutterBottom sx={{ color: '#666', mb: 3 }}>
                {language === 'en' ? 'Nutritious superfood with growing market demand' :
                 language === 'kn' ? 'ಬೆಳೆಯುತ್ತಿರುವ ಮಾರುಕಟ್ಟೆ ಬೇಡಿಕೆಯೊಂದಿಗೆ ಪೌಷ್ಟಿಕ ಸೂಪರ್‌ಫುಡ್' :
                 'बढ़ती बाजार मांग के साथ पौष्टिक सुपरफूड'}
              </Typography>
              <Typography variant="body1" paragraph>
                {language === 'en' ? 
                  'Avocados are increasingly popular worldwide due to their nutritional benefits and versatility. Our research center studies optimal growing conditions, variety selection, and sustainable farming practices for avocado cultivation in various climates.' :
                 language === 'kn' ? 
                  'ಅವಕಾಡೊಗಳು ಅವುಗಳ ಪೌಷ್ಟಿಕ ಪ್ರಯೋಜನಗಳು ಮತ್ತು ಬಹುಮುಖತೆಯಿಂದಾಗಿ ವಿಶ್ವಾದ್ಯಂತ ಹೆಚ್ಚು ಜನಪ್ರಿಯವಾಗುತ್ತಿವೆ. ನಮ್ಮ ಸಂಶೋಧನಾ ಕೇಂದ್ರವು ವಿವಿಧ ಹವಾಮಾನಗಳಲ್ಲಿ ಅವಕಾಡೊ ಕೃಷಿಗಾಗಿ ಅನುಕೂಲಕರ ಬೆಳವಣಿಗೆಯ ಪರಿಸ್ಥಿತಿಗಳು, ವಿವಿಧತೆ ಆಯ್ಕೆ ಮತ್ತು ಸುಸ್ಥಿರ ಕೃಷಿ ಅಭ್ಯಾಸಗಳನ್ನು ಅಧ್ಯಯನ ಮಾಡುತ್ತದೆ.' :
                  'एवोकाडो अपने पोषण संबंधी लाभों और बहुमुखी प्रतिभा के कारण दुनिया भर में तेजी से लोकप्रिय हो रहे हैं। हमारा अनुसंधान केंद्र विभिन्जलवायु में एवोकाडो की खेती के लिए इष्टतम उगाने की स्थितियों, किस्म चयन और स्थायी खेती प्रथाओं का अध्ययन करता है।'}
              </Typography>
              <Typography variant="body1" paragraph>
                {language === 'en' ? 
                  'Our avocado research highlights:' :
                 language === 'kn' ? 
                  'ನಮ್ಮ ಅವಕಾಡೊ ಸಂಶೋಧನೆಯ ಮುಖ್ಯಾಂಶಗಳು:' :
                  'हमारे एवोकाडो अनुसंधान के मुख्य बिंदु:'}
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                  {language === 'en' ? 
                    'Adaptation of avocado varieties to different soil and climate conditions' :
                   language === 'kn' ? 
                    'ವಿವಿಧ ಮಣ್ಣು ಮತ್ತು ಹವಾಮಾನ ಪರಿಸ್ಥಿತಿಗಳಿಗೆ ಅವಕಾಡೊ ವಿವಿಧತೆಗಳ ಹೊಂದಾಣಿಕೆ' :
                    'विभिन्न मिट्टी और जलवायु परिस्थितियों के लिए एवोकाडो किस्मों का अनुकूलन'}
                </Typography>
                <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                  {language === 'en' ? 
                    'Water-efficient irrigation systems for sustainable avocado farming' :
                   language === 'kn' ? 
                    'ಸುಸ್ಥಿರ ಅವಕಾಡೊ ಕೃಷಿಗಾಗಿ ನೀರು-ದಕ್ಷ ನೀರಾವರಿ ವ್ಯವಸ್ಥೆಗಳು' :
                    'स्थायी एवोकाडो खेती के लिए पानी-कुशल सिंचाई प्रणाली'}
                </Typography>
                <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                  {language === 'en' ? 
                    'Organic pest management strategies for chemical-free cultivation' :
                   language === 'kn' ? 
                    'ರಾಸಾಯನಿಕ-ಮುಕ್ತ ಕೃಷಿಗಾಗಿ ಸಾವಯವ ಕೀಟ ನಿರ್ವಹಣಾ ತಂತ್ರಗಳು' :
                    'रासायनिक-मुक्त खेती के लिए जैविक कीट प्रबंधन रणनीतियां'}
                </Typography>
                <Typography component="li" variant="body1">
                  {language === 'en' ? 
                    'Post-harvest handling techniques to extend shelf life and maintain quality' :
                   language === 'kn' ? 
                    'ಶೆಲ್ಫ್ ಲೈಫ್ ವಿಸ್ತರಿಸಲು ಮತ್ತು ಗುಣಮಟ್ಟವನ್ನು ಕಾಪಾಡಿಕೊಳ್ಳಲು ಕಟಾವಿನ ನಂತರದ ನಿರ್ವಹಣಾ ತಂತ್ರಗಳು' :
                    'शेल्फ लाइफ बढ़ाने और गुणवत्ता बनाए रखने के लिए कटाई के बाद की हैंडलिंग तकनीकें'}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Research Statistics Section */}
      <Box sx={{ py: 8, backgroundColor: '#f5f9f5' }}>
        <Container maxWidth="lg">
          <Typography variant="h3" component="h2" align="center" gutterBottom sx={{ fontWeight: 700, color: theme.palette.primary.main }}>
            {language === 'en' ? 'Research Impact & Statistics' :
             language === 'kn' ? 'ಸಂಶೋಧನಾ ಪರಿಣಾಮ ಮತ್ತು ಅಂಕಿಅಂಶಗಳು' :
             'अनुसंधान प्रभाव और आँकड़े'}
          </Typography>
          <Typography variant="h6" align="center" sx={{ color: '#666', mb: 6, maxWidth: '800px', mx: 'auto' }}>
            {language === 'en' ? 
              'Our research initiatives have transformed agricultural practices across multiple regions' :
             language === 'kn' ? 
              'ನಮ್ಮ ಸಂಶೋಧನಾ ಉಪಕ್ರಮಗಳು ಹಲವಾರು ಪ್ರದೇಶಗಳಲ್ಲಿ ಕೃಷಿ ಅಭ್ಯಾಸಗಳನ್ನು ಪರಿವರ್ತಿಸಿವೆ' :
              'हमारे अनुसंधान पहलों ने कई क्षेत्रों में कृषि प्रथाओं को बदल दिया है'}
          </Typography>
          
          <Grid container spacing={4} justifyContent="center">
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ 
                textAlign: 'center', 
                py: 3, 
                px: 2, 
                height: '100%',
                border: '1px solid rgba(27, 76, 53, 0.1)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 10px 25px rgba(27, 76, 53, 0.1)'
                }
              }}>
                <Avatar 
                  sx={{ 
                    mx: 'auto', 
                    mb: 2, 
                    width: 60, 
                    height: 60, 
                    bgcolor: 'rgba(27, 76, 53, 0.1)',
                    '&:hover': {
                      bgcolor: 'rgba(27, 76, 53, 0.2)'
                    }
                  }}
                >
                  <TimelineIcon sx={{ fontSize: 30, color: '#1B4C35' }} />
                </Avatar>
                <Typography variant="h4" component="div" sx={{ fontWeight: 700, color: '#1B4C35', mb: 1 }}>
                  15+
                </Typography>
                <Typography variant="body2" sx={{ color: '#666' }}>
                  {language === 'en' ? 'Years of Research' :
                   language === 'kn' ? 'ಸಂಶೋಧನೆಯ ವರ್ಷಗಳು' :
                   'शोध के वर्ष'}
                </Typography>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ 
                textAlign: 'center', 
                py: 3, 
                px: 2, 
                height: '100%',
                border: '1px solid rgba(27, 76, 53, 0.1)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 10px 25px rgba(27, 76, 53, 0.1)'
                }
              }}>
                <Avatar 
                  sx={{ 
                    mx: 'auto', 
                    mb: 2, 
                    width: 60, 
                    height: 60, 
                    bgcolor: 'rgba(27, 76, 53, 0.1)',
                    '&:hover': {
                      bgcolor: 'rgba(27, 76, 53, 0.2)'
                    }
                  }}
                >
                  <TrendingUpIcon sx={{ fontSize: 30, color: '#1B4C35' }} />
                </Avatar>
                <Typography variant="h4" component="div" sx={{ fontWeight: 700, color: '#1B4C35', mb: 1 }}>
                  300%
                </Typography>
                <Typography variant="body2" sx={{ color: '#666' }}>
                  {language === 'en' ? 'Yield Increase' :
                   language === 'kn' ? 'ಇಳುವರಿ ಹೆಚ್ಚಳ' :
                   'उत्पादन में वृद्धि'}
                </Typography>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ 
                textAlign: 'center', 
                py: 3, 
                px: 2, 
                height: '100%',
                border: '1px solid rgba(27, 76, 53, 0.1)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 10px 25px rgba(27, 76, 53, 0.1)'
                }
              }}>
                <Avatar 
                  sx={{ 
                    mx: 'auto', 
                    mb: 2, 
                    width: 60, 
                    height: 60, 
                    bgcolor: 'rgba(27, 76, 53, 0.1)',
                    '&:hover': {
                      bgcolor: 'rgba(27, 76, 53, 0.2)'
                    }
                  }}
                >
                  <PublicIcon sx={{ fontSize: 30, color: '#1B4C35' }} />
                </Avatar>
                <Typography variant="h4" component="div" sx={{ fontWeight: 700, color: '#1B4C35', mb: 1 }}>
                  50+
                </Typography>
                <Typography variant="body2" sx={{ color: '#666' }}>
                  {language === 'en' ? 'Partner Farms' :
                   language === 'kn' ? 'ಪಾಲುದಾರ ಕೃಷಿ ಕ್ಷೇತ್ರಗಳು' :
                   'साझेदार खेत'}
                </Typography>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ 
                textAlign: 'center', 
                py: 3, 
                px: 2, 
                height: '100%',
                border: '1px solid rgba(27, 76, 53, 0.1)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 10px 25px rgba(27, 76, 53, 0.1)'
                }
              }}>
                <Avatar 
                  sx={{ 
                    mx: 'auto', 
                    mb: 2, 
                    width: 60, 
                    height: 60, 
                    bgcolor: 'rgba(27, 76, 53, 0.1)',
                    '&:hover': {
                      bgcolor: 'rgba(27, 76, 53, 0.2)'
                    }
                  }}
                >
                  <ScienceIcon sx={{ fontSize: 30, color: '#1B4C35' }} />
                </Avatar>
                <Typography variant="h4" component="div" sx={{ fontWeight: 700, color: '#1B4C35', mb: 1 }}>
                  25+
                </Typography>
                <Typography variant="body2" sx={{ color: '#666' }}>
                  {language === 'en' ? 'Research Papers' :
                   language === 'kn' ? 'ಸಂಶೋಧನಾ ಪ್ರಬಂಧಗಳು' :
                   'शोध पत्र'}
                </Typography>
              </Card>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Research Methodologies Section */}
      <Box sx={{ py: 8, backgroundColor: '#fff' }}>
        <Container maxWidth="lg">
          <Typography variant="h3" component="h2" align="center" gutterBottom sx={{ fontWeight: 700, color: theme.palette.primary.main }}>
            {language === 'en' ? 'Our Research Methodologies' :
             language === 'kn' ? 'ನಮ್ಮ ಸಂಶೋಧನಾ ವಿಧಾನಗಳು' :
             'हमारी अनुसंधान विधियां'}
          </Typography>
          <Typography variant="h6" align="center" sx={{ color: '#666', mb: 6, maxWidth: '800px', mx: 'auto' }}>
            {language === 'en' ? 
              'Innovative approaches to sustainable agriculture and crop development' :
             language === 'kn' ? 
              'ಸುಸ್ಥಿರ ಕೃಷಿ ಮತ್ತು ಬೆಳೆ ಅಭಿವೃದ್ಧಿಗೆ ನವೀನ ವಿಧಾನಗಳು' :
              'स्थायी कृषि और फसल विकास के लिए नवीन दृष्टिकोण'}
          </Typography>
          
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Accordion sx={{ boxShadow: 'none', '&:before': { display: 'none' } }}>
                <AccordionSummary 
                  expandIcon={<ExpandMoreIcon sx={{ color: '#1B4C35' }} />}
                  aria-controls="panel1a-content"
                  id="panel1a-header"
                  sx={{ 
                    backgroundColor: 'rgba(27, 76, 53, 0.03)',
                    '&:hover': {
                      backgroundColor: 'rgba(27, 76, 53, 0.06)'
                    },
                    '&.Mui-expanded': {
                      backgroundColor: 'rgba(27, 76, 53, 0.08)'
                    }
                  }}
                >
                  <Typography variant="h6" sx={{ fontWeight: 600, color: '#1B4C35' }}>
                    {language === 'en' ? 'Sustainable Cultivation Techniques' :
                     language === 'kn' ? 'ಸುಸ್ಥಿರ ಕೃಷಿ ತಂತ್ರಗಳು' :
                     'स्थायी कृषी तकनीकें'}
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Typography variant="body2" sx={{ color: '#555' }}>
                    {language === 'en' ? 
                      'Our research focuses on developing cultivation methods that minimize environmental impact while maximizing yield. This includes organic farming practices, integrated pest management, and water conservation techniques.' :
                     language === 'kn' ? 
                      'ನಮ್ಮ ಸಂಶೋಧನೆಯು ಪರಿಸರ ಪರಿಣಾಮವನ್ನು ಕನಿಷ್ಠಗೊಳಿಸುವುದರೊಂದಿಗೆ ಇಳುವರಿಯನ್ನು ಗರಿಷ್ಠಗೊಳಿಸುವ ಕೃಷಿ ವಿಧಾನಗಳನ್ನು ಅಭಿವೃದ್ಧಿಪಡಿಸುವ ಮೇಲೆ ಕೇಂದ್ರೀಕರಿಸುತ್ತದೆ. ಇದರಲ್ಲಿ ಸಾವಯವ ಕೃಷಿ ಅಭ್ಯಾಸಗಳು, ಏಕೀಕೃತ ಕೀಟ ನಿರ್ವಹಣೆ ಮತ್ತು ನೀರಿನ ಸಂರಕ್ಷಣಾ ತಂತ್ರಗಳು ಸೇರಿವೆ.' :
                      'हमारा शोध पर्यावरणीय प्रभाव को कम करते हुए उपज को अधिकतम करने वाली खेती विधियों को विकसित करने पर केंद्रित है। इसमें जैविक खेती प्रथाएं, एकीकृत कीट प्रबंधन और पानी संरक्षण तकनीकें शामिल हैं।'}
                  </Typography>
                </AccordionDetails>
              </Accordion>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Accordion sx={{ boxShadow: 'none', '&:before': { display: 'none' } }}>
                <AccordionSummary 
                  expandIcon={<ExpandMoreIcon sx={{ color: '#1B4C35' }} />}
                  aria-controls="panel2a-content"
                  id="panel2a-header"
                  sx={{ 
                    backgroundColor: 'rgba(27, 76, 53, 0.03)',
                    '&:hover': {
                      backgroundColor: 'rgba(27, 76, 53, 0.06)'
                    },
                    '&.Mui-expanded': {
                      backgroundColor: 'rgba(27, 76, 53, 0.08)'
                    }
                  }}
                >
                  <Typography variant="h6" sx={{ fontWeight: 600, color: '#1B4C35' }}>
                    {language === 'en' ? 'Genetic Research & Development' :
                     language === 'kn' ? 'ಜೀವಶಾಸ್ತ್ರೀಯ ಸಂಶೋಧನೆ ಮತ್ತು ಅಭಿವೃದ್ಧಿ' :
                     'आनुवंशिक अनुसंधान और विकास'}
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Typography variant="body2" sx={{ color: '#555' }}>
                    {language === 'en' ? 
                      'Through advanced genetic research, we develop crop varieties with enhanced resistance to diseases, pests, and climate challenges. Our breeding programs focus on improving yield potential while maintaining nutritional quality.' :
                     language === 'kn' ? 
                      'ಮುಂದುವರಿದ ಜೀವಶಾಸ್ತ್ರೀಯ ಸಂಶೋಧನೆಯ ಮೂಲಕ, ನಾವು ರೋಗಗಳು, ಕೀಟಗಳು ಮತ್ತು ಹವಾಮಾನ ಸವಾಲುಗಳಿಗೆ ವಿಶೇಷ ಪ್ರತಿರೋಧಕ ಶಕ್ತಿಯುಳ್ಳ ಬೆಳೆ ವಿವಿಧತೆಗಳನ್ನು ಅಭಿವೃದ್ಧಿಪಡಿಸುತ್ತೇವೆ. ನಮ್ಮ ಸಂಕರೀಕರಣ ಕಾರ್ಯಕ್ರಮಗಳು ಪೌಷ್ಟಿಕ ಗುಣಮಟ್ಟವನ್ನು ಕಾಪಾಡಿಕೊಳ್ಳುವುದರೊಂದಿಗೆ ಇಳುವರಿ ಸಾಮರ್ಥ್ಯವನ್ನು ಸುಧಾರಿಸುವ ಮೇಲೆ ಕೇಂದ್ರೀಕರಿಸುತ್ತವೆ.' :
                      'उन्नत आनुवंशिक अनुसंधान के माध्यम से, हम रोगों, कीटों और जलवायु चुनौतियों के प्रति बेहतर प्रतिरोध के साथ फसल किस्मों का विकास करते हैं। हमारे पालन कार्यक्रम पोषण गुणवत्ता को बनाए रखते हुए उत्पादन क्षमता में सुधार पर केंद्रित हैं।'}
                  </Typography>
                </AccordionDetails>
              </Accordion>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Accordion sx={{ boxShadow: 'none', '&:before': { display: 'none' } }}>
                <AccordionSummary 
                  expandIcon={<ExpandMoreIcon sx={{ color: '#1B4C35' }} />}
                  aria-controls="panel3a-content"
                  id="panel3a-header"
                  sx={{ 
                    backgroundColor: 'rgba(27, 76, 53, 0.03)',
                    '&:hover': {
                      backgroundColor: 'rgba(27, 76, 53, 0.06)'
                    },
                    '&.Mui-expanded': {
                      backgroundColor: 'rgba(27, 76, 53, 0.08)'
                    }
                  }}
                >
                  <Typography variant="h6" sx={{ fontWeight: 600, color: '#1B4C35' }}>
                    {language === 'en' ? 'Soil & Water Management' :
                     language === 'kn' ? 'ಮಣ್ಣು ಮತ್ತು ನೀರಿನ ನಿರ್ವಹಣೆ' :
                     'मिट्टी और पानी प्रबंधन'}
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Typography variant="body2" sx={{ color: '#555' }}>
                    {language === 'en' ? 
                      'Our research center develops innovative soil health improvement techniques and water-efficient irrigation systems. We focus on creating sustainable water cycles and soil regeneration methods for long-term agricultural productivity.' :
                     language === 'kn' ? 
                      'ನಮ್ಮ ಸಂಶೋಧನಾ ಕೇಂದ್ರವು ನವೀನ ಮಣ್ಣಿನ ಆರೋಗ್ಯ ಸುಧಾರಣಾ ತಂತ್ರಗಳು ಮತ್ತು ನೀರಿನ-ದಕ್ಷ ನೀರಾವರಿ ವ್ಯವಸ್ಥೆಗಳನ್ನು ಅಭಿವೃದ್ಧಿಪಡಿಸುತ್ತದೆ. ನಾವು ದೀರ್ಘಕಾಲೀನ ಕೃಷಿ ಉತ್ಪಾದಕತೆಗಾಗಿ ಸುಸ್ಥಿರ ನೀರಿನ ಚಕ್ರಗಳು ಮತ್ತು ಮಣ್ಣಿನ ಪುನಃಜನನ ವಿಧಾನಗಳನ್ನು ಸೃಷ್ಟಿಸುವ ಮೇಲೆ ಕೇಂದ್ರೀಕರಿಸುತ್ತೇವೆ.' :
                      'हमारा अनुसंधान केंद्र नवीन मिट्टी स्वास्थ्य सुधार तकनीकों और पानी-कुशल सिंचाई प्रणालियों का विकास करता है। हम लंबी अवधि की कृषि उत्पादकता के लिए स्थायी पानी चक्र और मिट्टी पुनर्जनन विधियों पर ध्यान केंद्रित करते हैं'}
                  </Typography>
                </AccordionDetails>
              </Accordion>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Accordion sx={{ boxShadow: 'none', '&:before': { display: 'none' } }}>
                <AccordionSummary 
                  expandIcon={<ExpandMoreIcon sx={{ color: '#1B4C35' }} />}
                  aria-controls="panel4a-content"
                  id="panel4a-header"
                  sx={{ 
                    backgroundColor: 'rgba(27, 76, 53, 0.03)',
                    '&:hover': {
                      backgroundColor: 'rgba(27, 76, 53, 0.06)'
                    },
                    '&.Mui-expanded': {
                      backgroundColor: 'rgba(27, 76, 53, 0.08)'
                    }
                  }}
                >
                  <Typography variant="h6" sx={{ fontWeight: 600, color: '#1B4C35' }}>
                    {language === 'en' ? 'Post-Harvest Technology' :
                     language === 'kn' ? 'ಕಟಾವಿನ ನಂತರದ ತಂತ್ರಜ್ಞಾನ' :
                     'कटाई के बाद की तकनीक'}
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Typography variant="body2" sx={{ color: '#555' }}>
                    {language === 'en' ? 
                      'We develop advanced post-harvest handling techniques to minimize waste and extend shelf life. Our research includes cold chain optimization, packaging innovations, and value-added processing methods for agricultural products.' :
                     language === 'kn' ? 
                      'ತ್ಯಾಜ್ಯವನ್ನು ಕಡಿಮೆ ಮಾಡಲು ಮತ್ತು ಶೆಲ್ಫ್ ಲೈಫ್ ವಿಸ್ತರಿಸಲು ನಾವು ಮುಂದುವರಿದ ಕಟಾವಿನ ನಂತರದ ನಿರ್ವಹಣಾ ತಂತ್ರಗಳನ್ನು ಅಭಿವೃದ್ಧಿಪಡಿಸುತ್ತೇವೆ. ನಮ್ಮ ಸಂಶೋಧನೆಯು ಕೋಲ್ಡ್ ಚೇನ್ ಅನುಕೂಲತೆ, ಪ್ಯಾಕೇಜಿಂಗ್ ನವೀನತೆಗಳು ಮತ್ತು ಕೃಷಿ ಉತ್ಪನ್ನಗಳಿಗಾಗಿ ಮೌಲ್ಯ-ಸೇರಿಸಿದ ಸಂಸ್ಕರಣ ವಿಧಾನಗಳನ್ನು ಒಳಗೊಂಡಿದೆ.' :
                      'हम अपशिष्ट को कम करने और शेल्फ लाइफ बढ़ाने के लिए उन्नत पोस्ट-हार्वेस्ट हैंडलिंग तकनीकों का विकास करते हैं। हमारे शोध में कोल्ड चेन अनुकूलन, पैकेजिंग नवाचार और कृषि उत्पादों के लिए मूल्य-जोड़ी गई प्रसंस्करण विधियां शामिल हैं।'}
                  </Typography>
                </AccordionDetails>
              </Accordion>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Partnerships Section */}
      <Box sx={{ py: 8, backgroundColor: '#f5f9f5' }}>
        <Container maxWidth="lg">
          <Typography variant="h3" component="h2" align="center" gutterBottom sx={{ fontWeight: 700, color: theme.palette.primary.main }}>
            {language === 'en' ? 'Research Partnerships & Collaborations' :
             language === 'kn' ? 'ಸಂಶೋಧನಾ ಪಾಲುದಾರಿಕೆ ಮತ್ತು ಸಹಯೋಗ' :
             'शोध भागीदारी और सहयोग'}
          </Typography>
          <Typography variant="h6" align="center" sx={{ color: '#666', mb: 6, maxWidth: '800px', mx: 'auto' }}>
            {language === 'en' ? 
              'Working together with leading institutions to advance agricultural innovation' :
             language === 'kn' ? 
              'ಕೃಷಿ ನವೀಕರಣವನ್ನು ಮುಂದುವರಿಸಲು ಪ್ರಮುಖ ಸಂಸ್ಥೆಗಳೊಂದಿಗೆ ಒಟ್ಟಿಗೆ ಕೆಲಸ ಮಾಡುತ್ತಿದ್ದೇವೆ' :
              'कृषि नवाचार को आगे बढ़ाने के लिए प्रमुख संस्थाओं के साथ मिलकर काम कर रहे हैं'}
          </Typography>
          
          <Grid container spacing={4} justifyContent="center">
            <Grid item xs={12} sm={6} md={4}>
              <Card sx={{ 
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                border: '1px solid rgba(27, 76, 53, 0.1)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 10px 25px rgba(27, 76, 53, 0.1)'
                }
              }}>
                <CardContent sx={{ flexGrow: 1, p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar 
                      sx={{ 
                        mr: 2, 
                        width: 50, 
                        height: 50, 
                        bgcolor: 'rgba(27, 76, 53, 0.1)',
                        '&:hover': {
                          bgcolor: 'rgba(27, 76, 53, 0.2)'
                        }
                      }}
                    >
                      <AgroIcon sx={{ fontSize: 24, color: '#1B4C35' }} />
                    </Avatar>
                    <Typography variant="h6" component="div" sx={{ fontWeight: 600, color: '#1B4C35' }}>
                      {language === 'en' ? 'Agricultural Universities' :
                       language === 'kn' ? 'ಕೃಷಿ ವಿಶ್ವವಿದ್ಯಾಲಯಗಳು' :
                       'कृषि विश्वविद्यालय'}
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ color: '#555', mb: 2 }}>
                    {language === 'en' ? 
                      'Collaborating with leading agricultural universities for cutting-edge research and knowledge exchange.' :
                     language === 'kn' ? 
                      'ಅಗ್ರಗಾಮಿ ಸಂಶೋಧನೆ ಮತ್ತು ಜ್ಞಾನ ವಿನಿಮಯಕ್ಕಾಗಿ ಪ್ರಮುಖ ಕೃಷಿ ವಿಶ್ವವಿದ್ಯಾಲಯಗಳೊಂದಿಗೆ ಸಹಯೋಗ.' :
                      'अग्रणी अनुसंधन और ज्ञान विनिमय के लिए प्रमुख कृषि विश्वविद्यालयों के साथ सहयोग।'}
                  </Typography>
                  <Chip 
                    label={language === 'en' ? '5 Partners' : language === 'kn' ? '5 ಪಾಲುದಾರರು' : '5 साझेदार'} 
                    size="small" 
                    sx={{ 
                      bgcolor: 'rgba(27, 76, 53, 0.1)', 
                      color: '#1B4C35',
                      fontWeight: 500
                    }} 
                  />
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={4}>
              <Card sx={{ 
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                border: '1px solid rgba(27, 76, 53, 0.1)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 10px 25px rgba(27, 76, 53, 0.1)'
                }
              }}>
                <CardContent sx={{ flexGrow: 1, p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar 
                      sx={{ 
                        mr: 2, 
                        width: 50, 
                        height: 50, 
                        bgcolor: 'rgba(27, 76, 53, 0.1)',
                        '&:hover': {
                          bgcolor: 'rgba(27, 76, 53, 0.2)'
                        }
                      }}
                    >
                      <ScienceIcon sx={{ fontSize: 24, color: '#1B4C35' }} />
                    </Avatar>
                    <Typography variant="h6" component="div" sx={{ fontWeight: 600, color: '#1B4C35' }}>
                      {language === 'en' ? 'Research Institutes' :
                       language === 'kn' ? 'ಸಂಶೋಧನಾ ಸಂಸ್ಥೆಗಳು' :
                       'शोध संस्थान'}
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ color: '#555', mb: 2 }}>
                    {language === 'en' ? 
                      'Partnering with national and international research institutes for comprehensive agricultural studies.' :
                     language === 'kn' ? 
                      'ಸಮಗ್ರ ಕೃಷಿ ಅಧ್ಯಯನಗಳಿಗಾಗಿ ರಾಷ್ಟ್ರೀಯ ಮತ್ತು ಅಂತರರಾಷ್ಟ್ರೀಯ ಸಂಶೋಧನಾ ಸಂಸ್ಥೆಗಳೊಂದಿಗೆ ಪಾಲುದಾರಿಕೆ.' :
                      'संपूर्ण कृषि अध्ययनों के लिए राष्ट्रीय और अंतरराष्ट्रीय शोध संस्थानों के साथ साझेदारी।'}
                  </Typography>
                  <Chip 
                    label={language === 'en' ? '8 Partners' : language === 'kn' ? '8 ಪಾಲುದಾರರು' : '8 साझेदार'} 
                    size="small" 
                    sx={{ 
                      bgcolor: 'rgba(27, 76, 53, 0.1)', 
                      color: '#1B4C35',
                      fontWeight: 500
                    }} 
                  />
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={4}>
              <Card sx={{ 
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                border: '1px solid rgba(27, 76, 53, 0.1)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 10px 25px rgba(27, 76, 53, 0.1)'
                }
              }}>
                <CardContent sx={{ flexGrow: 1, p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar 
                      sx={{ 
                        mr: 2, 
                        width: 50, 
                        height: 50, 
                        bgcolor: 'rgba(27, 76, 53, 0.1)',
                        '&:hover': {
                          bgcolor: 'rgba(27, 76, 53, 0.2)'
                        }
                      }}
                    >
                      <PublicIcon sx={{ fontSize: 24, color: '#1B4C35' }} />
                    </Avatar>
                    <Typography variant="h6" component="div" sx={{ fontWeight: 600, color: '#1B4C35' }}>
                      {language === 'en' ? 'Government Agencies' :
                       language === 'kn' ? 'ಸರ್ಕಾರಿ ಸಂಸ್ಥೆಗಳು' :
                       'सरकारी एजेंसियां'}
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ color: '#555', mb: 2 }}>
                    {language === 'en' ? 
                      'Working closely with government agencies to implement research findings at a national scale.' :
                     language === 'kn' ? 
                      'ರಾಷ್ಟ್ರೀಯ ಮಟ್ಟದಲ್ಲಿ ಸಂಶೋಧನಾ ಫಲಿತಾಂಶಗಳನ್ನು ಅನುಷ್ಠಾನಗೊಳಿಸಲು ಸರ್ಕಾರಿ ಸಂಸ್ಥೆಗಳೊಂದಿಗೆ ನಿಕಟವಾಗಿ ಕೆಲಸ ಮಾಡುತ್ತಿದ್ದೇವೆ.' :
                      'राष्ट्रीय स्तर पर शोध निष್कर्षों को लागू करने के लिए सरकारी एजेंसियों के साथ निकटता से काम कर रहे हैं।'}
                  </Typography>
                  <Chip 
                    label={language === 'en' ? '3 Partners' : language === 'kn' ? '3 ಪಾಲುದಾರರು' : '3 साझेदार'} 
                    size="small" 
                    sx={{ 
                      bgcolor: 'rgba(27, 76, 53, 0.1)', 
                      color: '#1B4C35',
                      fontWeight: 500
                    }} 
                  />
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Contact Section */}
      <Box sx={{ py: 6, backgroundColor: '#f9f9f9' }}>
        <Container maxWidth="lg">
          <Typography variant="h4" component="h2" align="center" gutterBottom sx={{ fontWeight: 700, color: theme.palette.primary.main }}>
            {language === 'en' ? 'Interested in Our Research?' :
             language === 'kn' ? 'ನಮ್ಮ ಸಂಶೋಧನೆಯಲ್ಲಿ ಆಸಕ್ತಿ ಇದೆಯೇ?' :
             'हमारे अनुसंधान में रुचि है?'}
          </Typography>
          <Typography variant="body1" align="center" sx={{ mb: 4, maxWidth: '800px', mx: 'auto' }}>
            {language === 'en' ? 
              'Contact us to learn more about our research initiatives or to schedule a visit to our research center.' :
             language === 'kn' ? 
              'ನಮ್ಮ ಸಂಶೋಧನಾ ಉಪಕ್ರಮಗಳ ಬಗ್ಗೆ ಹೆಚ್ಚಿನ ಮಾಹಿತಿಗಾಗಿ ಅಥವಾ ನಮ್ಮ ಸಂಶೋಧನಾ ಕೇಂದ್ರಕ್ಕೆ ಭೇಟಿಯನ್ನು ನಿಗದಿಪಡಿಸಲು ನಮ್ಮನ್ನು ಸಂಪರ್ಕಿಸಿ.' :
              'हमारे अनुसंधान पहलों के बारे में अधिक जानने या हमारे अनुसंधान केंद्र में यात्रा निर्धारित करने के लिए हमसे संपर्क करें।'}
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center' }}>
            <Button
              component={Link}
              to="/contact"
              variant="contained"
              color="primary"
              size="large"
              endIcon={<ArrowForwardIcon />}
              sx={{ 
                borderRadius: 28,
                px: 4,
                py: 1.5,
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '1rem'
              }}
            >
              {language === 'en' ? 'Contact Us' :
               language === 'kn' ? 'ನಮ್ಮನ್ನು ಸಂಪರ್ಕಿಸಿ' :
               'संपर्क करें'}
            </Button>
          </Box>
        </Container>
      </Box>

      <Footer />
    </Box>
  );
};

export default ResearchCenter;
