# Netlify Configuration for Darvi Group Production Deployment
# Single deployment with Netlify Functions for darvigroup.in

[build]
  command = "npm install && npm run build"
  publish = "build"
  functions = "netlify/functions"

[build.environment]
  NODE_ENV = "production"
  CI = "false"
  SKIP_PREFLIGHT_CHECK = "true"
  ESLINT_NO_DEV_ERRORS = "true"
  GENERATE_SOURCEMAP = "false"
  NETLIFY_NEXT_PLUGIN_SKIP = "true"
  REACT_APP_ENVIRONMENT = "production"
  REACT_APP_DOMAIN = "darvigroup.in"
  # REACT_APP_GEMINI_API_KEY should be set in Netlify environment variables
  REACT_APP_GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent"



# This is a React app, not Next.js - Next.js plugin should be disabled via environment variable

# Security headers for production
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "geolocation=(), microphone=(), camera=()"

    # HTTPS enforcement
    Strict-Transport-Security = "max-age=31536000; includeSubDomains; preload"

    # Content Security Policy for production with PayU integration and Gemini AI
    Content-Security-Policy = """
      default-src 'self';
      script-src 'self' 'unsafe-inline' 'unsafe-eval' https://secure.payu.in https://test.payu.in https://*.payu.in https://identity.netlify.com https://www.googletagmanager.com https://www.google-analytics.com https://unpkg.com https://cdn.jsdelivr.net https://*.netlify.com https://*.netlify.app https://*.cloudinary.com https://api.cloudinary.com https://darvigroup.in;
      style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com https://cdn.jsdelivr.net;
      font-src 'self' https://fonts.gstatic.com https://unpkg.com https://cdn.jsdelivr.net;
      img-src 'self' data: https: blob: https://*.cloudinary.com https://darvigroup.in;
      connect-src 'self' https://darvigroup.in https://secure.payu.in https://test.payu.in https://*.payu.in https://info.payu.in https://script.google.com https://script.googleusercontent.com https://www.google-analytics.com https://analytics.google.com https://formsubmit.co https://api.emailjs.com https://api.web3forms.com https://api.netlify.com https://*.netlify.com https://*.netlify.app https://api.github.com https://uploads.github.com https://generativelanguage.googleapis.com https://api.weatherapi.com https://*.cloudinary.com https://api.cloudinary.com;
      frame-src 'self' https://secure.payu.in https://test.payu.in https://*.payu.in https://identity.netlify.com https://*.netlify.com https://*.netlify.app https://www.google.com;
      object-src 'none';
      base-uri 'self';
      form-action 'self' https://secure.payu.in/_payment https://test.payu.in/_payment https://secure.payu.in https://test.payu.in https://*.payu.in https://api.netlify.com;
    """

# Cache optimization for static assets
[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"



# PayU Payment Callback Handlers (MUST BE FIRST)
# Handle POST requests from PayU
[[redirects]]
  from = "/payu/success"
  to = "/.netlify/functions/payu-callback-handler"
  status = 200
  force = true
  conditions = {Method = ["GET", "POST"]}

[[redirects]]
  from = "/payu/failure"
  to = "/.netlify/functions/payu-callback-handler"
  status = 200
  force = true
  conditions = {Method = ["GET", "POST"]}

[[redirects]]
  from = "/payu/cancel"
  to = "/.netlify/functions/payu-callback-handler"
  status = 200
  force = true
  conditions = {Method = ["GET", "POST"]}

# PayU webhook handler
[[redirects]]
  from = "/payu/webhook"
  to = "/.netlify/functions/payu-webhook-handler"
  status = 200
  force = true

# Custom domain redirects
[[redirects]]
  from = "https://www.darvigroup.in/*"
  to = "https://darvigroup.in/:splat"
  status = 301
  force = true

# Admin panel redirect
[[redirects]]
  from = "/admin/*"
  to = "/admin/index.html"
  status = 200

# Specific headers for admin panel to ensure Netlify CMS works properly
[[headers]]
  for = "/admin/*"
  [headers.values]
    # Security headers with relaxed CSP for Netlify CMS
    Content-Security-Policy = """
      default-src 'self';
      script-src 'self' 'unsafe-inline' 'unsafe-eval' https://secure.payu.in https://test.payu.in https://*.payu.in https://identity.netlify.com https://www.googletagmanager.com https://www.google-analytics.com https://unpkg.com https://cdn.jsdelivr.net https://*.netlify.com https://*.netlify.app https://*.cloudinary.com https://api.cloudinary.com https://darvigroup.in;
      style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com https://cdn.jsdelivr.net;
      font-src 'self' https://fonts.gstatic.com https://unpkg.com https://cdn.jsdelivr.net;
      img-src 'self' data: https: blob: https://*.cloudinary.com https://darvigroup.in;
      connect-src 'self' https://darvigroup.in https://secure.payu.in https://test.payu.in https://*.payu.in https://info.payu.in https://script.google.com https://script.googleusercontent.com https://www.google-analytics.com https://analytics.google.com https://formsubmit.co https://api.emailjs.com https://api.web3forms.com https://api.netlify.com https://*.netlify.com https://*.netlify.app https://api.github.com https://uploads.github.com https://generativelanguage.googleapis.com https://api.weatherapi.com https://*.cloudinary.com https://api.cloudinary.com;
      frame-src 'self' https://secure.payu.in https://test.payu.in https://*.payu.in https://identity.netlify.com https://*.netlify.com https://*.netlify.app https://www.google.com;
      object-src 'none';
      base-uri 'self';
      form-action 'self' https://secure.payu.in/_payment https://test.payu.in/_payment https://secure.payu.in https://test.payu.in https://*.payu.in https://api.netlify.com;
    """

# SPA routing (must be last)
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200


