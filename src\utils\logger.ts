/**
 * Production-Safe Logger for React Frontend
 * Provides logging functionality that is completely silent in production
 * Only logs in development environment to prevent console output in production
 */

const isDevelopment = process.env.NODE_ENV === 'development';
const isDebugEnabled = process.env.REACT_APP_DEBUG === 'true' || isDevelopment;

/**
 * Safe console.log - only logs in development
 */
export const log = (...args: any[]) => {
  if (isDevelopment) {
    // eslint-disable-next-line no-console
    console.log(...args);
  }
};

/**
 * Safe console.warn - only logs in development
 */
export const warn = (...args: any[]) => {
  if (isDevelopment) {
    // eslint-disable-next-line no-console
    console.warn(...args);
  }
};

/**
 * Safe console.error - only logs critical errors in development
 * In production, errors are still thrown but not logged to console
 */
export const error = (...args: any[]) => {
  if (isDevelopment) {
    // eslint-disable-next-line no-console
    console.error(...args);
  }
};

/**
 * Debug logging - only when explicitly enabled
 */
export const debug = (...args: any[]) => {
  if (isDebugEnabled) {
    // eslint-disable-next-line no-console
    console.debug(...args);
  }
};

/**
 * Info logging - only in development
 */
export const info = (...args: any[]) => {
  if (isDevelopment) {
    // eslint-disable-next-line no-console
    console.info(...args);
  }
};

/**
 * Silent logger for production - all methods are no-ops
 */
const silentLogger = {
  log: () => {},
  warn: () => {},
  error: () => {},
  debug: () => {},
  info: () => {}
};

/**
 * Development logger - full console functionality
 */
const devLogger = {
  log,
  warn,
  error,
  debug,
  info
};

/**
 * Export appropriate logger based on environment
 */
export const logger = isDevelopment ? devLogger : silentLogger;

/**
 * Log payment operations - development only
 */
export const logPaymentOperation = (operation: string, data: any = {}) => {
  if (!isDevelopment) return;

  const timestamp = new Date().toISOString();
  // eslint-disable-next-line no-console
  console.log(`[${timestamp}] [PAYMENT] ${operation}:`, data);
};

/**
 * Log API calls - development only
 */
export const logApiCall = (method: string, url: string, data?: any) => {
  if (!isDevelopment) return;

  // eslint-disable-next-line no-console
  console.log(`[API] ${method} ${url}`, data ? data : '');
};

/**
 * Log component lifecycle - development only
 */
export const logComponent = (componentName: string, action: string, data?: any) => {
  if (!isDevelopment) return;

  // eslint-disable-next-line no-console
  console.log(`[COMPONENT] ${componentName} - ${action}`, data ? data : '');
};

/**
 * Log errors with context - development only
 */
export const logError = (message: string, context: any = {}) => {
  if (!isDevelopment) return;

  // eslint-disable-next-line no-console
  console.error(`❌ ${message}:`, context);
};

/**
 * Log warnings with context - development only
 */
export const logWarning = (message: string, context: any = {}) => {
  if (!isDevelopment) return;

  // eslint-disable-next-line no-console
  console.warn(`⚠️ ${message}:`, context);
};

/**
 * Log success messages - development only
 */
export const logSuccess = (message: string, context: any = {}) => {
  if (!isDevelopment) return;

  // eslint-disable-next-line no-console
  console.log(`✅ ${message}:`, context);
};

/**
 * Environment checks
 */
export { isDevelopment, isDebugEnabled };

/**
 * Default export - the logger object
 */
export default logger;
