import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Avatar,
  useTheme,
  useMediaQuery,
  keyframes,
  IconButton,
  Skeleton
} from '@mui/material';
import {
  Article as ArticleIcon,
  ArrowForward as ArrowForwardIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';
import { useBlogContent, BlogPost } from '../hooks/useCMSContent';

// Animation keyframes
const fadeInUp = keyframes`
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const slideInLeft = keyframes`
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
`;

const Blog: React.FC = () => {
  const theme = useTheme();
  const { language } = useLanguage();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [animate, setAnimate] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Load blog content from CMS
  const { blogContent, loading } = useBlogContent();

  useEffect(() => {
    setAnimate(true);
  }, []);

  // Sample blog data (fallback if CMS content is not available)
  const sampleBlogPosts: BlogPost[] = [
    {
      id: 1,
      title: {
        en: 'Sustainable Farming Practices for Modern Agriculture',
        kn: 'ಆಧುನಿಕ ಕೃಷಿಗಾಗಿ ಸುಸ್ಥಿರ ಕೃಷಿ ಅಭ್ಯಾಸಗಳು',
        hi: 'आधुनिक कृषि के लिए स्थायी खेती प्रथाएं'
      },
      excerpt: {
        en: 'Discover innovative sustainable farming techniques that are revolutionizing modern agriculture while preserving our environment.',
        kn: 'ನಮ್ಮ ಪರಿಸರವನ್ನು ಸಂರಕ್ಷಿಸುತ್ತಾ ಆಧುನಿಕ ಕೃಷಿಯನ್ನು ಕ್ರಾಂತಿಕಾರಕಗೊಳಿಸುತ್ತಿರುವ ನವೀನ ಸುಸ್ಥಿರ ಕೃಷಿ ತಂತ್ರಗಳನ್ನು ಅನ್ವೇಷಿಸಿ.',
        hi: 'हमारे पर्यावरण को संरक्षित करते हुए आधुनिक कृषि को क्रांतिकारी बना रही नवीन स्थायी खेती तकनीकों की खोज करें।'
      },
      category: {
        en: 'Sustainable Farming',
        kn: 'ಸುಸ್ಥಿರ ಕೃಷಿ',
        hi: 'स्थायी खेती'
      },
      author: {
        en: 'Dr. Rajesh Kumar',
        kn: 'ಡಾ. ರಾಜೇಶ್ ಕುಮಾರ್',
        hi: 'डॉ. राजेश कुमार'
      },
      date: '2024-01-15',
      readTime: {
        en: '5 min read',
        kn: '5 ನಿಮಿಷ ಓದು',
        hi: '5 मिनट पढ़ें'
      },
      image: '/darvi-images/field1.png',
      featured: true
    },
    {
      id: 2,
      title: {
        en: 'IoT Technology in Agricultural Management',
        kn: 'ಕೃಷಿ ನಿರ್ವಹಣೆಯಲ್ಲಿ ಐಒಟಿ ತಂತ್ರಜ್ಞಾನ',
        hi: 'कृषि प्रबंधन में आईओटी तकनीक'
      },
      excerpt: {
        en: 'How Internet of Things is transforming agricultural practices and improving crop yields through smart monitoring.',
        kn: 'ಇಂಟರ್ನೆಟ್ ಆಫ್ ಥಿಂಗ್ಸ್ ಹೇಗೆ ಕೃಷಿ ಅಭ್ಯಾಸಗಳನ್ನು ರೂಪಾಂತರಗೊಳಿಸುತ್ತಿದೆ ಮತ್ತು ಸ್ಮಾರ್ಟ್ ಮಾನಿಟರಿಂಗ್ ಮೂಲಕ ಬೆಳೆ ಇಳುವರಿಯನ್ನು ಸುಧಾರಿಸುತ್ತಿದೆ.',
        hi: 'इंटरनेट ऑफ थिंग्स कैसे स्मार्ट मॉनिटरिंग के माध्यम से कृषि प्रथाओं को बदल रहा है और फसल उपज में सुधार कर रहा है।'
      },
      category: {
        en: 'Technology',
        kn: 'ತಂತ್ರಜ್ಞಾನ',
        hi: 'तकनीक'
      },
      author: {
        en: 'Prof. Priya Sharma',
        kn: 'ಪ್ರೊ. ಪ್ರಿಯಾ ಶರ್ಮಾ',
        hi: 'प्रो. प्रिया शर्मा'
      },
      date: '2024-01-10',
      readTime: {
        en: '7 min read',
        kn: '7 ನಿಮಿಷ ಓದು',
        hi: '7 मिनट पढ़ें'
      },
      image: '/darvi-images/iot-device-agriculture.jpg',
      featured: false
    },
    {
      id: 3,
      title: {
        en: 'Organic Farming: Benefits and Implementation',
        kn: 'ಸಾವಯವ ಕೃಷಿ: ಪ್ರಯೋಜನಗಳು ಮತ್ತು ಅನುಷ್ಠಾನ',
        hi: 'जैविक खेती: लाभ और कार्यान्वयन'
      },
      excerpt: {
        en: 'A comprehensive guide to organic farming methods, their benefits, and how to implement them in your agricultural practices.',
        kn: 'ಸಾವಯವ ಕೃಷಿ ವಿಧಾನಗಳು, ಅವುಗಳ ಪ್ರಯೋಜನಗಳು ಮತ್ತು ನಿಮ್ಮ ಕೃಷಿ ಅಭ್ಯಾಸಗಳಲ್ಲಿ ಅವುಗಳನ್ನು ಹೇಗೆ ಅನುಷ್ಠಾನಗೊಳಿಸಬೇಕೆಂದು ಸಂಪೂರ್ಣ ಮಾರ್ಗದರ್ಶಿ.',
        hi: 'जैविक खेती के तरीकों, उनके लाभों और उन्हें अपनी कृषि प्रथाओं में कैसे लागू करें, इसकी एक व्यापक मार्गदर्शिका।'
      },
      category: {
        en: 'Organic Farming',
        kn: 'ಸಾವಯವ ಕೃಷಿ',
        hi: 'जैविक खेती'
      },
      author: {
        en: 'Dr. Meera Patel',
        kn: 'ಡಾ. ಮೀರಾ ಪಟೇಲ್',
        hi: 'डॉ. मीरा पटेल'
      },
      date: '2024-01-05',
      readTime: {
        en: '6 min read',
        kn: '6 ನಿಮಿಷ ಓದು',
        hi: '6 मिनट पढ़ें'
      },
      image: '/darvi-images/field2.jpg',
      featured: false
    },
    {
      id: 4,
      title: {
        en: 'Water Management in Agriculture',
        kn: 'ಕೃಷಿಯಲ್ಲಿ ನೀರಿನ ನಿರ್ವಹಣೆ',
        hi: 'कृषि में जल प्रबंधन'
      },
      excerpt: {
        en: 'Effective water management strategies for sustainable agriculture and improved crop productivity.',
        kn: 'ಸುಸ್ಥಿರ ಕೃಷಿ ಮತ್ತು ಸುಧಾರಿತ ಬೆಳೆ ಉತ್ಪಾದಕತೆಗಾಗಿ ಪರಿಣಾಮಕಾರಿ ನೀರಿನ ನಿರ್ವಹಣೆ ತಂತ್ರಗಳು.',
        hi: 'स्थायी कृषि और बेहतर फसल उत्पादकता के लिए प्रभावी जल प्रबंधन रणनीतियां।'
      },
      category: {
        en: 'Water Management',
        kn: 'ನೀರಿನ ನಿರ್ವಹಣೆ',
        hi: 'जल प्रबंधन'
      },
      author: {
        en: 'Dr. Amit Singh',
        kn: 'ಡಾ. ಅಮಿತ್ ಸಿಂಗ್',
        hi: 'डॉ. अमित सिंह'
      },
      date: '2023-12-28',
      readTime: {
        en: '4 min read',
        kn: '4 ನಿಮಿಷ ಓದು',
        hi: '4 मिनट पढ़ें'
      },
      image: '/darvi-images/field3.jpg',
      featured: false
    }
  ];

  // Use CMS content if available, otherwise use sample data
  const blogPosts = blogContent?.posts || sampleBlogPosts;

  const categories = [
    { value: 'all', label: { en: 'All', kn: 'ಎಲ್ಲಾ', hi: 'सभी' } },
    { value: 'sustainable', label: { en: 'Sustainable Farming', kn: 'ಸುಸ್ಥಿರ ಕೃಷಿ', hi: 'स्थायी खेती' } },
    { value: 'technology', label: { en: 'Technology', kn: 'ತಂತ್ರಜ್ಞಾನ', hi: 'तकनीक' } },
    { value: 'organic', label: { en: 'Organic Farming', kn: 'ಸಾವಯವ ಕೃಷಿ', hi: 'जैविक खेती' } },
    { value: 'water', label: { en: 'Water Management', kn: 'ನೀರಿನ ನಿರ್ವಹಣೆ', hi: 'जल प्रबंधन' } }
  ];

  const filteredPosts = blogPosts.filter((post: BlogPost) => {
    const postCategory = getLocalizedText(post.category);
    const matchesCategory = selectedCategory === 'all' || 
      postCategory.toLowerCase().includes(selectedCategory);
    
    const matchesSearch = searchQuery === '' || 
      getLocalizedText(post.title).toLowerCase().includes(searchQuery.toLowerCase()) ||
      getLocalizedText(post.excerpt).toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesCategory && matchesSearch;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'kn' ? 'kn-IN' : language === 'hi' ? 'hi-IN' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getLocalizedText = (textObj: any) => {
    if (typeof textObj === 'string') return textObj;
    return textObj?.[language as keyof typeof textObj] || textObj?.en || '';
  };

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #1B4C35 0%, #2E7D32 100%)',
          color: 'white',
          py: { xs: 6, md: 8 },
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'url(/darvi-images/field1.png)',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            opacity: 0.1,
            zIndex: 1
          }
        }}
      >
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          <Box
            sx={{
              textAlign: 'center',
              animation: animate ? `${fadeInUp} 0.8s ease-out` : 'none'
            }}
          >
            <Avatar
              sx={{
                width: { xs: 60, md: 80 },
                height: { xs: 60, md: 80 },
                mx: 'auto',
                mb: 3,
                bgcolor: 'rgba(255,255,255,0.2)',
                animation: `${pulse} 2s infinite`
              }}
            >
              <ArticleIcon sx={{ fontSize: { xs: 30, md: 40 } }} />
            </Avatar>
            <Typography
              variant={isMobile ? 'h3' : 'h2'}
              sx={{
                fontWeight: 700,
                mb: 2,
                background: 'linear-gradient(45deg, #fff, #e8f5e8)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textShadow: '0 2px 4px rgba(0,0,0,0.3)'
              }}
            >
              {blogContent?.hero?.title?.[language as keyof typeof blogContent.hero.title] || 
               (language === 'en' ? 'Agricultural Blog' :
                language === 'kn' ? 'ಕೃಷಿ ಬ್ಲಾಗ್' :
                'कृषि ब्लॉग')}
            </Typography>
            <Typography
              variant="h6"
              sx={{
                opacity: 0.9,
                maxWidth: 600,
                mx: 'auto',
                lineHeight: 1.6
              }}
            >
              {blogContent?.hero?.subtitle?.[language as keyof typeof blogContent.hero.subtitle] ||
               (language === 'en' ? 'Insights, innovations, and best practices in modern agriculture' :
                language === 'kn' ? 'ಆಧುನಿಕ ಕೃಷಿಯಲ್ಲಿ ಒಳನೋಟಗಳು, ನವೀಕರಣಗಳು ಮತ್ತು ಉತ್ತಮ ಅಭ್ಯಾಸಗಳು' :
                'आधुनिक कृषि में अंतर्दृष्टि, नवाचार और सर्वोत्तम प्रथाएं')}
            </Typography>
          </Box>
        </Container>
      </Box>

      {/* Search and Filter Section */}
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            gap: 2,
            mb: 4,
            animation: animate ? `${slideInLeft} 0.6s ease-out` : 'none'
          }}
        >
          {/* Search Bar */}
          <Box sx={{ flex: 1, position: 'relative' }}>
            <SearchIcon
              sx={{
                position: 'absolute',
                left: 12,
                top: '50%',
                transform: 'translateY(-50%)',
                color: 'text.secondary',
                zIndex: 1
              }}
            />
            <input
              type="text"
              placeholder={language === 'en' ? 'Search articles...' :
                         language === 'kn' ? 'ಲೇಖನಗಳನ್ನು ಹುಡುಕಿ...' :
                         'लेख खोजें...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{
                width: '100%',
                padding: '12px 12px 12px 45px',
                border: '1px solid #e0e0e0',
                borderRadius: '8px',
                fontSize: '16px',
                outline: 'none',
                transition: 'border-color 0.3s ease'
              }}
            />
          </Box>

          {/* Category Filter */}
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {categories.map((category) => (
              <Chip
                key={category.value}
                label={getLocalizedText(category.label)}
                onClick={() => setSelectedCategory(category.value)}
                sx={{
                  bgcolor: selectedCategory === category.value ? 'primary.main' : 'grey.100',
                  color: selectedCategory === category.value ? 'white' : 'text.primary',
                  '&:hover': {
                    bgcolor: selectedCategory === category.value ? 'primary.dark' : 'grey.200'
                  }
                }}
              />
            ))}
          </Box>
        </Box>

        {/* Blog Posts Grid */}
        <Grid container spacing={3}>
          {loading ? (
            // Loading skeletons
            Array.from({ length: 6 }).map((_, index: number) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card sx={{ height: '100%' }}>
                  <Skeleton variant="rectangular" height={200} />
                  <CardContent>
                    <Skeleton variant="text" height={24} sx={{ mb: 1 }} />
                    <Skeleton variant="text" height={20} sx={{ mb: 1 }} />
                    <Skeleton variant="text" height={16} sx={{ mb: 2 }} />
                    <Skeleton variant="text" height={16} />
                  </CardContent>
                </Card>
              </Grid>
            ))
          ) : filteredPosts.length > 0 ? (
            filteredPosts.map((post: BlogPost, index: number) => (
              <Grid item xs={12} sm={6} md={4} key={post.id || index}>
                <Card
                  sx={{
                    height: '100%',
                    transition: 'all 0.3s ease',
                    cursor: 'pointer',
                    animation: animate ? `${fadeInUp} ${0.3 + index * 0.1}s ease-out` : 'none',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 12px 40px rgba(0,0,0,0.15)'
                    }
                  }}
                  component={Link}
                  to={`/blog/${post.id}`}
                  style={{ textDecoration: 'none' }}
                >
                  <CardMedia
                    component="img"
                    height="200"
                    image={post.image}
                    alt={getLocalizedText(post.title)}
                    sx={{ objectFit: 'cover' }}
                  />
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Chip
                        label={getLocalizedText(post.category)}
                        size="small"
                        sx={{
                          bgcolor: 'primary.main',
                          color: 'white',
                          fontSize: '0.75rem'
                        }}
                      />
                      {post.featured && (
                        <Chip
                          label={language === 'en' ? 'Featured' :
                                 language === 'kn' ? 'ವೈಶಿಷ್ಟ್ಯ' :
                                 'विशेष'}
                          size="small"
                          sx={{
                            bgcolor: 'secondary.main',
                            color: 'white',
                            fontSize: '0.75rem',
                            ml: 1
                          }}
                        />
                      )}
                    </Box>

                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        mb: 2,
                        lineHeight: 1.3,
                        color: 'text.primary',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden'
                      }}
                    >
                      {getLocalizedText(post.title)}
                    </Typography>

                    <Typography
                      variant="body2"
                      sx={{
                        color: 'text.secondary',
                        mb: 3,
                        lineHeight: 1.5,
                        display: '-webkit-box',
                        WebkitLineClamp: 3,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden'
                      }}
                    >
                      {getLocalizedText(post.excerpt)}
                    </Typography>

                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Avatar
                          sx={{ width: 24, height: 24, fontSize: '0.75rem' }}
                        >
                          {getLocalizedText(post.author)?.charAt(0)}
                        </Avatar>
                        <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                          {getLocalizedText(post.author)}
                        </Typography>
                      </Box>
                      <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                        {formatDate(post.date)}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 2 }}>
                      <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                        {getLocalizedText(post.readTime)}
                      </Typography>
                      <IconButton
                        size="small"
                        sx={{
                          color: 'primary.main',
                          '&:hover': {
                            bgcolor: 'primary.light',
                            color: 'white'
                          }
                        }}
                      >
                        <ArrowForwardIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))
          ) : (
            // No results found
            <Grid item xs={12}>
              <Box
                sx={{
                  textAlign: 'center',
                  py: 8,
                  color: 'text.secondary'
                }}
              >
                <ArticleIcon sx={{ fontSize: 64, mb: 2, opacity: 0.5 }} />
                <Typography variant="h6" sx={{ mb: 1 }}>
                  {language === 'en' ? 'No articles found' :
                   language === 'kn' ? 'ಯಾವುದೇ ಲೇಖನಗಳು ಕಂಡುಬಂದಿಲ್ಲ' :
                   'कोई लेख नहीं मिला'}
                </Typography>
                <Typography variant="body2">
                  {language === 'en' ? 'Try adjusting your search or filter criteria' :
                   language === 'kn' ? 'ನಿಮ್ಮ ಹುಡುಕಾಟ ಅಥವಾ ಫಿಲ್ಟರ್ ಮಾನದಂಡಗಳನ್ನು ಸರಿಪಡಿಸಲು ಪ್ರಯತ್ನಿಸಿ' :
                   'अपने खोज या फ़िल्टर मानदंडों को समायोजित करने का प्रयास करें'}
                </Typography>
              </Box>
            </Grid>
          )}
        </Grid>
      </Container>
    </Box>
  );
};

export default Blog; 