/**
 * PayU Hash Generator Utility
 * Modular implementation for hash generation and validation
 * Based on Official PayU Documentation: https://docs.payu.in/docs/generate-hash-payu-hosted
 */

const crypto = require('crypto');
const { log, error, logHashOperation } = require('./productionLogger');

/**
 * Generate payment hash for PayU according to official documentation
 * Format: sha512(key|txnid|amount|productinfo|firstname|email|udf1|udf2|udf3|udf4|udf5||||||SALT)
 * 
 * @param {Object} paymentData - Payment data object
 * @param {string} salt - PayU merchant salt
 * @returns {string} - Generated hash in lowercase
 */
const generatePaymentHash = (paymentData, salt) => {
  try {
    // Extract required parameters with defaults
    const {
      key,
      txnid,
      amount,
      productinfo,
      firstname,
      email,
      udf1 = '',
      udf2 = '',
      udf3 = '',
      udf4 = '',
      udf5 = ''
    } = paymentData;

    // Validate required parameters
    if (!key || !txnid || !amount || !productinfo || !firstname || !email || !salt) {
      throw new Error('Missing required parameters for hash generation');
    }

    // Construct hash string exactly as per PayU documentation
    // Note: The empty pipes (||||||) are crucial and must be included
    const hashString = `${key}|${txnid}|${amount}|${productinfo}|${firstname}|${email}|${udf1}|${udf2}|${udf3}|${udf4}|${udf5}||||||${salt}`;

    // Generate SHA512 hash and return in lowercase
    const hash = crypto.createHash('sha512').update(hashString).digest('hex').toLowerCase();

    // Debug logging - only in development
    logHashOperation('Generation', {
      hashString: hashString.replace(salt, '[SALT]'), // Hide salt in logs
      hashLength: hash.length,
      hashPreview: hash.substring(0, 10) + '...'
    });

    return hash;
  } catch (err) {
    error('Hash generation error:', err);
    throw new Error(`Hash generation failed: ${err.message}`);
  }
};

/**
 * Generate reverse hash for PayU response verification
 * Format: sha512(SALT|status||||||udf5|udf4|udf3|udf2|udf1|email|firstname|productinfo|amount|txnid|key)
 * 
 * @param {Object} responseData - Response data from PayU
 * @param {string} salt - PayU merchant salt
 * @returns {string} - Generated reverse hash in lowercase
 */
const generateReverseHash = (responseData, salt) => {
  try {
    // Extract required parameters with defaults
    const {
      key,
      txnid,
      amount,
      productinfo,
      firstname,
      email,
      status,
      udf1 = '',
      udf2 = '',
      udf3 = '',
      udf4 = '',
      udf5 = ''
    } = responseData;

    // Validate required parameters
    if (!key || !txnid || !amount || !productinfo || !firstname || !email || !status || !salt) {
      throw new Error('Missing required parameters for reverse hash generation');
    }

    // Construct reverse hash string exactly as per PayU documentation
    // Note: The order is reversed and empty pipes (||||||) are crucial
    const hashString = `${salt}|${status}||||||${udf5}|${udf4}|${udf3}|${udf2}|${udf1}|${email}|${firstname}|${productinfo}|${amount}|${txnid}|${key}`;

    // Generate SHA512 hash and return in lowercase
    const hash = crypto.createHash('sha512').update(hashString).digest('hex').toLowerCase();

    // Debug logging - only in development
    logHashOperation('Reverse Generation', {
      hashString: hashString.replace(salt, '[SALT]'), // Hide salt in logs
      hashLength: hash.length,
      hashPreview: hash.substring(0, 10) + '...'
    });

    return hash;
  } catch (err) {
    error('Reverse hash generation error:', err);
    throw new Error(`Reverse hash generation failed: ${err.message}`);
  }
};

/**
 * Verify PayU response hash
 * 
 * @param {Object} responseData - Response data from PayU
 * @param {string} receivedHash - Hash received from PayU
 * @param {string} salt - PayU merchant salt
 * @returns {Object} - Verification result
 */
const verifyPayUHash = (responseData, receivedHash, salt) => {
  try {
    // Generate expected hash
    const expectedHash = generateReverseHash(responseData, salt);
    
    // Compare hashes (case-insensitive)
    const isValid = expectedHash.toLowerCase() === receivedHash.toLowerCase();
    
    // Log verification result - only in development
    logHashOperation('Verification', {
      isValid,
      expectedHashPreview: expectedHash.substring(0, 10) + '...',
      receivedHashPreview: receivedHash.substring(0, 10) + '...',
      txnid: responseData.txnid
    });
    
    return {
      isValid,
      expectedHash,
      receivedHash,
      message: isValid ? 'Hash verification successful' : 'Hash verification failed'
    };
  } catch (err) {
    error('Hash verification error:', err);
    return {
      isValid: false,
      error: err.message,
      message: 'Hash verification failed due to error'
    };
  }
};

/**
 * Validate hash generation parameters
 * 
 * @param {Object} params - Parameters for hash generation
 * @returns {Object} - Validation result
 */
const validateHashParams = (params) => {
  const errors = [];
  const required = ['key', 'txnid', 'amount', 'productinfo', 'firstname', 'email'];
  
  required.forEach(field => {
    if (!params[field] || params[field].toString().trim() === '') {
      errors.push(`${field} is required for hash generation`);
    }
  });
  
  // Validate amount format
  if (params.amount && isNaN(parseFloat(params.amount))) {
    errors.push('Amount must be a valid number');
  }
  
  // Validate email format
  if (params.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(params.email)) {
    errors.push('Email must be in valid format');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

module.exports = {
  generatePaymentHash,
  generateReverseHash,
  verifyPayUHash,
  validateHashParams
};
