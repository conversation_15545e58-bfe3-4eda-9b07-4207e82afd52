/**
 * PayU Hash Generator - Official Documentation Compliant
 * Based on: https://docs.payu.in/docs/generate-hash-payu-hosted
 *
 * CRITICAL: This implementation follows the EXACT PayU specification
 * Hash Format: key|txnid|amount|productinfo|firstname|email|udf1|udf2|udf3|udf4|udf5||||||salt
 * Response Hash: salt|status||||||udf5|udf4|udf3|udf2|udf1|email|firstname|productinfo|amount|txnid|key
 *
 * IMPORTANT NOTES:
 * - PayU uses SHA-512 hash function (belongs to SHA-2 family)
 * - Hash must be in lowercase
 * - There are exactly 6 empty pipes after udf5 (for udf6-udf10 and additional fields)
 * - Salt must NEVER be exposed in frontend requests
 * - This follows PayU Hosted Checkout integration method
 *
 * Last Updated: Based on PayU Documentation as of 2024
 * Version: 2.0 (Production Ready)
 */

const crypto = require('crypto');

/**
 * Generate PayU payment request hash according to official specification
 * @param {Object} params - Payment parameters
 * @param {string} salt - PayU merchant salt
 * @returns {string} SHA-512 hash in lowercase
 */
function generatePaymentHash(params, salt) {
  // Step 1: Extract and validate required parameters
  const requiredParams = ['key', 'txnid', 'amount', 'productinfo', 'firstname', 'email'];
  for (const param of requiredParams) {
    if (!params[param]) {
      throw new Error(`Missing required parameter: ${param}`);
    }
  }

  // Step 2: Convert all parameters to strings (PayU requirement)
  const key = String(params.key || '');
  const txnid = String(params.txnid || '');
  const amount = String(params.amount || '');
  const productinfo = String(params.productinfo || '');
  const firstname = String(params.firstname || '');
  const email = String(params.email || '');
  
  // Step 3: Handle UDF parameters (User Defined Fields)
  // If not provided, they must be empty strings, not null or undefined
  const udf1 = String(params.udf1 || '');
  const udf2 = String(params.udf2 || '');
  const udf3 = String(params.udf3 || '');
  const udf4 = String(params.udf4 || '');
  const udf5 = String(params.udf5 || '');

  // Step 4: Construct hash string with EXACT PayU specification
  // Format: key|txnid|amount|productinfo|firstname|email|udf1|udf2|udf3|udf4|udf5||||||salt
  // CRITICAL: There are exactly 6 empty pipes after udf5 (for udf6-udf10 and additional fields)
  const hashString = `${key}|${txnid}|${amount}|${productinfo}|${firstname}|${email}|${udf1}|${udf2}|${udf3}|${udf4}|${udf5}||||||${salt}`;

  // Step 5: Generate SHA-512 hash and convert to lowercase (PayU requirement)
  const hash = crypto.createHash('sha512').update(hashString, 'utf8').digest('hex').toLowerCase();

  // Hash generation completed

  return hash;
}

/**
 * Generate PayU response verification hash (reverse hash)
 * @param {Object} response - PayU response parameters
 * @param {string} salt - PayU merchant salt
 * @returns {string} SHA-512 hash in lowercase
 */
function generateResponseHash(response, salt) {
  // Step 1: Extract response parameters
  const status = String(response.status || '');
  const udf5 = String(response.udf5 || '');
  const udf4 = String(response.udf4 || '');
  const udf3 = String(response.udf3 || '');
  const udf2 = String(response.udf2 || '');
  const udf1 = String(response.udf1 || '');
  const email = String(response.email || '');
  const firstname = String(response.firstname || '');
  const productinfo = String(response.productinfo || '');
  const amount = String(response.amount || '');
  const txnid = String(response.txnid || '');
  const key = String(response.key || '');

  // Step 2: Construct reverse hash string with EXACT PayU specification
  // Format: salt|status||||||udf5|udf4|udf3|udf2|udf1|email|firstname|productinfo|amount|txnid|key
  // CRITICAL: There are exactly 6 empty pipes after status
  const hashString = `${salt}|${status}||||||${udf5}|${udf4}|${udf3}|${udf2}|${udf1}|${email}|${firstname}|${productinfo}|${amount}|${txnid}|${key}`;

  // Step 3: Generate SHA-512 hash and convert to lowercase
  const hash = crypto.createHash('sha512').update(hashString, 'utf8').digest('hex').toLowerCase();

  // Response hash generation completed

  return hash;
}

/**
 * Verify PayU response hash
 * @param {Object} response - PayU response parameters
 * @param {string} salt - PayU merchant salt
 * @returns {boolean} True if hash is valid
 */
function verifyResponseHash(response, salt) {
  try {
    const calculatedHash = generateResponseHash(response, salt);
    const receivedHash = String(response.hash || '').toLowerCase();
    
    const isValid = calculatedHash === receivedHash;

    // Hash verification completed
    
    return isValid;
  } catch (error) {
    // Hash verification error
    return false;
  }
}

/**
 * Validate hash generation parameters
 * @param {Object} params - Parameters to validate
 * @returns {Object} Validation result
 */
function validateHashParams(params) {
  const errors = [];
  const requiredFields = ['key', 'txnid', 'amount', 'productinfo', 'firstname', 'email'];
  
  // Check required fields
  for (const field of requiredFields) {
    if (!params[field] || String(params[field]).trim() === '') {
      errors.push(`${field} is required and cannot be empty`);
    }
  }
  
  // Validate email format
  if (params.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(params.email)) {
    errors.push('Invalid email format');
  }
  
  // Validate amount format
  if (params.amount && (isNaN(parseFloat(params.amount)) || parseFloat(params.amount) <= 0)) {
    errors.push('Amount must be a positive number');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

module.exports = {
  generatePaymentHash,
  generateResponseHash,
  verifyResponseHash,
  validateHashParams
};
